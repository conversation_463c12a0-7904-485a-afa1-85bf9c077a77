import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";
import PrintComponents from "react-print-components";
import React, { useEffect, useState, useCallback } from "react";
import dayjs from "dayjs";
import { Button, Col, Row, Table, Typography, DatePicker, Space, Card, Statistic, Alert, Spin, message, Progress } from "antd";
import { PrinterOutlined, DollarOutlined, ReloadOutlined } from "@ant-design/icons";


/**
 * PERFORMANCE OPTIMIZED: Landlord Statement Component
*
* A simple, effective landlord statement that just works with performance optimizations.
*/

// PERFORMANCE FIX: Add caching utilities
const dataCache = new Map();
const CACHE_TTL = 30000; // 30 seconds cache

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const NewLandlordStatement = (props) => {
  const { landlordId, pouchDatabase, databasePrefix } = props;

  // State management
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(1, 'year'),
    dayjs()
  ]);
  const [summary, setSummary] = useState({
    openingBalance: 0,
    totalIncome: 0,
    totalExpenses: 0,
    closingBalance: 0,
    transactionCount: 0
  });
  const [overallStats, setOverallStats] = useState({
    totalUnits: 0,
    occupiedUnits: 0,
    lifetimeIncome: 0,
    lifetimeExpenses: 0,
    currentBalance: 0
  });
  // Add progress state
  const [progress, setProgress] = useState({ current: 0, total: 0, message: '' });

  // Simple function to calculate landlord portion after management fee
  const calculateLandlordPortion = (amount, managementPercentage) => {
    const fee = (amount * (managementPercentage || 0)) / 100;
    return amount - fee;
  };

  // Simple function to check if date is in range
  const isDateInRange = (date, startDate, endDate) => {
    const d = dayjs(date);
    return d.isSameOrAfter(startDate, 'day') && d.isSameOrBefore(endDate, 'day');
  };

  // Break processing into chunks with progress updates
  const processDataInChunks = async (data, processor, chunkSize = 100) => {
    const results = [];
    const total = data.length;

    for (let i = 0; i < total; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const processed = chunk.map(processor);
      results.push(...processed);

      // Update progress and yield control
      setProgress({
        current: Math.min(i + chunkSize, total),
        total,
        message: `Processing ${Math.min(i + chunkSize, total)} of ${total} records...`
      });

      // Yield control to prevent freezing
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return results;
  };

  // SIMPLE & FAST: Direct database queries with filters
  const fetchStatementDataFast = useCallback(async () => {
    if (!landlordId || !pouchDatabase) {
      setError('Missing required data');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.time('⚡ Simple Fast Landlord Statement');

      // STEP 1: Load data with optimized approach
      const [allProperties, allUnits] = await Promise.all([
        pouchDatabase("properties", databasePrefix).getAllData({ conflicts: false, attachments: false }),
        pouchDatabase("units", databasePrefix).getAllData({ conflicts: false, attachments: false })
      ]);

      // STEP 2: Filter landlord data immediately (fast in-memory filtering)
      const properties = allProperties.filter(p => p.landlord?.value === landlordId);

      console.log(`[Landlord Statement] Debug:`, {
        landlordId,
        totalProperties: allProperties.length,
        landlordProperties: properties.length,
        sampleProperty: allProperties[0]?.landlord
      });

      if (properties.length === 0) {
        console.log(`[Landlord Statement] No properties found for landlord ${landlordId}`);
        setData([]);
        setSummary({ openingBalance: 0, totalIncome: 0, totalExpenses: 0, closingBalance: 0, transactionCount: 0 });
        setLoading(false);
        return;
      }

      const propertyIds = new Set(properties.map(p => p._id));
      const units = allUnits.filter(u => u.property && propertyIds.has(u.property.value));
      const unitIds = new Set(units.map(u => u._id));

      // STEP 3: Load transaction data including tenancy history for former tenants
      const [allOccupations, allTenancyHistory, allReceipts, allExpenses, allInvoices] = await Promise.all([
        pouchDatabase("occupations", databasePrefix).getAllData({ conflicts: false, attachments: false }),
        pouchDatabase("tenancy_history", databasePrefix).getAllData({ conflicts: false, attachments: false }),
        pouchDatabase("receipts", databasePrefix).getAllData({ conflicts: false, attachments: false }),
        pouchDatabase("expenses", databasePrefix).getAllData({ conflicts: false, attachments: false }),
        pouchDatabase("invoices", databasePrefix).getAllData({ conflicts: false, attachments: false })
      ]);

      // STEP 4: Include both current and historical occupations
      const currentOccupations = allOccupations.filter(o => o.unit && unitIds.has(o.unit.value));
      const historicalOccupations = allTenancyHistory.filter(h => h.unit && unitIds.has(h.unit.value));

      // Get all occupation IDs (current + historical)
      const currentOccupationIds = new Set(currentOccupations.map(o => o._id));
      const historicalOccupationIds = new Set(historicalOccupations.map(h => h.original_occupation_id).filter(Boolean));
      const allOccupationIds = new Set([...currentOccupationIds, ...historicalOccupationIds]);

      console.log(`[Landlord Statement] Occupation IDs:`, {
        currentOccupations: currentOccupations.length,
        historicalOccupations: historicalOccupations.length,
        totalOccupationIds: allOccupationIds.size
      });

      const receipts = allReceipts.filter(r =>
        r.occupancy && allOccupationIds.has(r.occupancy.value) &&
        !r.is_reversed && r.receipt_type !== 'Utility'
      );

      console.log(`[Landlord Statement] Filtered receipts:`, {
        totalReceipts: allReceipts.length,
        filteredReceipts: receipts.length,
        sampleReceipt: receipts[0]
      });

      const expenses = allExpenses.filter(e =>
        e.offset === true && (
          (e.property && propertyIds.has(e.property.value)) ||
          (e.unit && unitIds.has(e.unit.value)) ||
          (e.supplier?.value === landlordId)
        )
      );

      const invoices = allInvoices.filter(i => i.client?.value === landlordId);

      // STEP 4: Data is already filtered by database queries - no need for additional filtering!

      // STEP 5: Simple processing with pre-filtered data
      const transactions = [];
      const aggregates = {
        openingIncome: 0,
        openingExpenses: 0,
        periodIncome: 0,
        periodExpenses: 0
      };

      const startDate = dateRange[0];
      const endDate = dateRange[1];

      // Create lookup maps once
      const propertyMap = new Map(properties.map(p => [p._id, p]));
      const unitMap = new Map(units.map(u => [u._id, u]));
      const occupationMap = new Map([
        ...currentOccupations.map(o => [o._id, o]),
        ...historicalOccupations.map(h => [h.original_occupation_id, h])
      ]);

      // Process receipts - already filtered
      for (const receipt of receipts) {
        const receiptDate = dayjs(receipt.date);
        const occupation = occupationMap.get(receipt.occupancy.value);
        if (!occupation) continue;

        const unit = unitMap.get(occupation.unit.value);
        if (!unit) continue;

        const property = propertyMap.get(unit.property.value);
        if (!property) continue;

        const landlordAmount = receipt.receipt_type === 'Security Deposit'
          ? receipt.amount || 0
          : calculateLandlordPortion(receipt.amount || 0, property.management_percentage || 0);

        if (receiptDate.isBefore(startDate, 'day')) {
          aggregates.openingIncome += landlordAmount;
        } else if (receiptDate.isSameOrBefore(endDate, 'day')) {
          aggregates.periodIncome += landlordAmount;
          transactions.push({
            id: receipt._id,
            date: receiptDate.valueOf(), // Use timestamp for faster sorting
            dateFormatted: receiptDate.format('DD MMM YYYY'),
            type: 'In',
            property: property.name || 'Unknown',
            unit: unit.code || unit.name || 'Unknown',
            tenant: receipt.client?.label || 'Unknown',
            amount: landlordAmount,
            particulars: `${receipt.receipt_type || 'Receipt'} payment`
          });
        }
      }

      // Process expenses - already filtered
      for (const expense of expenses) {
        const expenseDate = dayjs(expense.expense_Date || expense.date);
        const amount = expense.amount || 0;

        if (expenseDate.isBefore(startDate, 'day')) {
          aggregates.openingExpenses += amount;
        } else if (expenseDate.isSameOrBefore(endDate, 'day')) {
          aggregates.periodExpenses += amount;

          const property = expense.property ? propertyMap.get(expense.property.value) : null;
          const unit = expense.unit ? unitMap.get(expense.unit.value) : null;

          transactions.push({
            id: expense._id,
            date: expenseDate.valueOf(),
            dateFormatted: expenseDate.format('DD MMM YYYY'),
            type: 'Out',
            property: property?.name || 'Property Expense',
            unit: unit?.code || unit?.name || 'N/A',
            tenant: expense.supplier?.label || 'Unknown',
            amount: -amount,
            particulars: expense.description || expense.particulars || 'Property expense'
          });
        }
      }

      // Process invoices - already filtered
      for (const invoice of invoices) {
        const invoiceDate = dayjs(invoice.date);
        const amount = Math.abs(invoice.amount || 0);
        const isIncome = (invoice.amount || 0) < 0;

        if (invoiceDate.isBefore(startDate, 'day')) {
          if (isIncome) aggregates.openingIncome += amount;
          else aggregates.openingExpenses += amount;
        } else if (invoiceDate.isSameOrBefore(endDate, 'day')) {
          if (isIncome) {
            aggregates.periodIncome += amount;
            transactions.push({
              id: invoice._id,
              date: invoiceDate.valueOf(),
              dateFormatted: invoiceDate.format('DD MMM YYYY'),
              type: 'In',
              property: 'Invoice to Landlord',
              unit: 'N/A',
              tenant: 'Management Company',
              amount: amount,
              particulars: invoice.particulars || 'Invoice to landlord'
            });
          } else {
            aggregates.periodExpenses += amount;
            transactions.push({
              id: invoice._id,
              date: invoiceDate.valueOf(),
              dateFormatted: invoiceDate.format('DD MMM YYYY'),
              type: 'Out',
              property: 'Invoice from Landlord',
              unit: 'N/A',
              tenant: 'Management Company',
              amount: -amount,
              particulars: invoice.particulars || 'Invoice from landlord'
            });
          }
        }
      }

      // Ultra-fast sort using timestamps
      transactions.sort((a, b) => a.date - b.date);

      // Calculate running balances in single pass
      const openingBalance = aggregates.openingIncome - aggregates.openingExpenses;
      let runningBalance = openingBalance;

      const finalTransactions = [
        {
          id: 'opening',
          dateFormatted: `Opening Balance as of ${startDate.format('DD MMM YYYY')}`,
          type: 'Balance',
          property: '',
          unit: '',
          tenant: '',
          amount: 0,
          particulars: 'Opening Balance',
          balance: Math.round(openingBalance),
          isOpeningBalance: true
        },
        ...transactions.map(t => {
          runningBalance += t.amount;
          return {
            ...t,
            date: t.dateFormatted, // Convert back to formatted date
            balance: Math.round(runningBalance)
          };
        })
      ];

      const closingBalance = openingBalance + aggregates.periodIncome - aggregates.periodExpenses;

      setData(finalTransactions);
      setCurrentPage(Math.ceil(finalTransactions.length / pageSize) || 1);

      setSummary({
        openingBalance: Math.round(openingBalance),
        totalIncome: Math.round(aggregates.periodIncome),
        totalExpenses: Math.round(aggregates.periodExpenses),
        closingBalance: Math.round(closingBalance),
        transactionCount: transactions.length
      });

      setOverallStats({
        totalUnits: units.length,
        occupiedUnits: units.filter(u =>
          currentOccupations.some(o => o.unit?.value === u._id)
        ).length,
        lifetimeIncome: Math.round(aggregates.openingIncome + aggregates.periodIncome),
        lifetimeExpenses: Math.round(aggregates.openingExpenses + aggregates.periodExpenses),
        currentBalance: Math.round(closingBalance)
      });

      console.timeEnd('⚡ Simple Fast Landlord Statement');
      console.log(`⚡ Processed ${finalTransactions.length} transactions with database filtering`);

    } catch (error) {
      console.error('Error in ultra-fast statement generation:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [landlordId, pouchDatabase, databasePrefix, dateRange, pageSize]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    fetchStatementDataFast();
  }, [fetchStatementDataFast]);

  // Handle date range change
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchStatementDataFast();
  };

  // Handle pagination change
  const handlePaginationChange = (page, size) => {
    setCurrentPage(page);
    if (size !== pageSize) {
      setPageSize(size);
      // Recalculate current page when page size changes
      const newTotalPages = Math.ceil(data.length / size);
      if (page > newTotalPages) {
        setCurrentPage(newTotalPages || 1);
      }
    }
  };

  // Simple table columns
  const columns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      width: 120
    },
    {
      title: "Property",
      dataIndex: "property",
      key: "property",
      width: 120
    },
    {
      title: "Unit",
      dataIndex: "unit",
      key: "unit",
      width: 80,
      render: (unit) => unit || "N/A"
    },
    {
      title: "Payee/Recipient",
      dataIndex: "tenant",
      key: "tenant",
      width: 120,
      ellipsis: true
    },
    {
      title: "Particulars",
      dataIndex: "particulars",
      key: "particulars",
      ellipsis: true
    },
    {
      title: "Income",
      dataIndex: "amount",
      key: "income",
      width: 100,
      align: "right",
      render: (amount, record) => {
        if (record.isOpeningBalance) return null;
        return record.type === "In" ? (
          <Text style={{ color: "green", fontWeight: 'bold' }}>
            {Math.abs(amount).toLocaleString()}
          </Text>
        ) : null;
      }
    },
    {
      title: "Expense",
      dataIndex: "amount",
      key: "expense",
      width: 100,
      align: "right",
      render: (amount, record) => {
        if (record.isOpeningBalance) return null;
        return record.type === "Out" ? (
          <Text style={{ color: "red", fontWeight: 'bold' }}>
            {Math.abs(amount).toLocaleString()}
          </Text>
        ) : null;
      }
    },
    {
      title: "Running Balance",
      dataIndex: "balance",
      key: "balance",
      width: 120,
      align: "right",
      render: (balance, record) => (
        <Text
          strong={record.isOpeningBalance}
          style={{ color: balance >= 0 ? "green" : "red" }}
        >
          {balance.toLocaleString()}
        </Text>
      )
    }
  ];

  // Overall landlord statistics cards
  const OverallStatsCards = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Units"
            value={overallStats.totalUnits}
            valueStyle={{ color: "#1890ff" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Occupied Units"
            value={`${overallStats.occupiedUnits}/${overallStats.totalUnits}`}
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Occupancy Rate"
            value={overallStats.totalUnits > 0 ? Math.round((overallStats.occupiedUnits / overallStats.totalUnits) * 100) : 0}
            suffix="%"
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Current Balance"
            value={overallStats.currentBalance?.toLocaleString() || "0"}
            prefix={<DollarOutlined />}
            valueStyle={{ color: (overallStats.currentBalance || 0) >= 0 ? "#52c41a" : "#f5222d" }}
          />
        </Card>
      </Col>
    </Row>
  );

  // Period summary cards component
  const PeriodSummaryCards = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="Opening Balance"
            value={Math.round(summary.openingBalance).toLocaleString()}
            prefix={<DollarOutlined />}
            valueStyle={{ color: summary.openingBalance >= 0 ? "#52c41a" : "#f5222d" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Period Income"
            value={Math.round(summary.totalIncome).toLocaleString()}
            prefix={<DollarOutlined />}
            valueStyle={{ color: "#52c41a" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Period Expenses"
            value={Math.round(summary.totalExpenses).toLocaleString()}
            prefix={<DollarOutlined />}
            valueStyle={{ color: "#f5222d" }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Closing Balance"
            value={Math.round(summary.closingBalance).toLocaleString()}
            prefix={<DollarOutlined />}
            valueStyle={{ color: summary.closingBalance >= 0 ? "#52c41a" : "#f5222d" }}
          />
        </Card>
      </Col>
    </Row>
  );



  // Simple error handling
  if (!landlordId || !pouchDatabase) {
    return <Alert message="Missing required data" type="error" />;
  }

  if (error) {
    return (
      <Alert
        message="Error Loading Statement"
        description={error}
        type="error"
        action={<Button size="small" onClick={handleRefresh}>Retry</Button>}
      />
    );
  }

  return (
    <div>
      {/* Controls */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Space>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear={false}
              format="DD MMM YYYY"
              disabled={loading}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            />
          </Space>
        </Col>
        <Col span={12} style={{ textAlign: "right" }}>
          <PrintComponents
            trigger={
              <Button
                icon={<PrinterOutlined />}
                type="primary"
                disabled={loading || data.length === 0}
              >
                Print
              </Button>
            }
          >
            <div style={{ padding: 24 }}>
              <DocumentHead />
              <Title level={3} style={{ textAlign: "center", marginBottom: 24 }}>
                Landlord Statement
              </Title>
              <Text>
                Period: {dateRange[0].format("DD MMM YYYY")} to {dateRange[1].format("DD MMM YYYY")}
              </Text>

              <OverallStatsCards />
              <PeriodSummaryCards />

              <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                pagination={false}
                size="small"
                bordered
              />
            </div>
          </PrintComponents>
        </Col>
      </Row>

      {/* Overall Landlord Statistics */}
      <OverallStatsCards />

      {/* Period Summary Cards */}
      <PeriodSummaryCards />

      {/* Statement Table */}
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: pageSize,
            current: currentPage,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} transactions`,
            onChange: handlePaginationChange,
            onShowSizeChange: handlePaginationChange
          }}
          size="middle"
          bordered
          rowClassName={(record) => record.isOpeningBalance ? 'opening-balance-row' : ''}
        />
      </Spin>

      <style jsx="true">{`
        .opening-balance-row {
          background-color: #f5f5f5;
          font-weight: bold;
        }
      `}</style>
    </div>
  );
};

export default NewLandlordStatement;
