/**
 * PERFORMANCE FIX: Web Worker Usage Examples for All Apps
 * Demonstrates how to use the modular web worker system across different apps
 */

import dataProcessingManager from './DataProcessingManager.js';

/**
 * Example 1: Prosy App - Process property management data
 */
export async function prosyExample() {
  console.log('🏠 [Prosy Example] Processing property management data...');
  
  // Sample data (normally from your database)
  const unitsData = [/* your units data */];
  const tenantsData = [/* your tenants data */];
  const propertiesData = [/* your properties data */];
  const occupationsData = [/* your occupations data */];
  
  try {
    // Process units with web worker (automatically uses worker for >50 items)
    const processedUnits = await dataProcessingManager.processBuffUnits(
      unitsData,
      tenantsData,
      propertiesData,
      occupationsData,
      'all' // selectedBranch
    );
    
    console.log(`✅ Processed ${processedUnits.length} units with enhanced data`);
    return processedUnits;
    
  } catch (error) {
    console.error('❌ Prosy processing failed:', error);
    return unitsData; // Return original data on error
  }
}

/**
 * Example 2: ZenWrench App - Process service management data
 */
export async function zenWrenchExample() {
  console.log('🔧 [ZenWrench Example] Processing service management data...');
  
  // Sample data (normally from your database)
  const invoicesData = [/* your invoices data */];
  const customersData = [/* your customers data */];
  const servicesData = [/* your services data */];
  
  try {
    // Process invoices with web worker (automatically uses worker for >50 items)
    const processedInvoices = await dataProcessingManager.processZenWrenchInvoices(
      invoicesData,
      customersData,
      servicesData,
      { appName: 'zenwrench' }
    );
    
    console.log(`✅ Processed ${processedInvoices.length} invoices with customer data`);
    return processedInvoices;
    
  } catch (error) {
    console.error('❌ ZenWrench processing failed:', error);
    return invoicesData; // Return original data on error
  }
}

/**
 * Example 3: Generic App - Process any table data
 */
export async function genericTableExample(tableData, relatedData = {}, appName = 'generic') {
  console.log(`📊 [${appName} Example] Processing table data...`);
  
  try {
    // Process any table data with web worker (automatically uses worker for >100 items)
    const processedData = await dataProcessingManager.processTableData(
      tableData,
      relatedData,
      { 
        appName,
        threshold: 100, // Custom threshold
        includeMetadata: true 
      }
    );
    
    console.log(`✅ Processed ${processedData.length} table rows`);
    return processedData;
    
  } catch (error) {
    console.error(`❌ ${appName} table processing failed:`, error);
    return tableData; // Return original data on error
  }
}

/**
 * Example 4: Universal Data Processing - Works with any operation
 */
export async function universalExample(operation, data, options = {}) {
  console.log(`🌐 [Universal Example] Processing ${operation}...`);
  
  try {
    // Process any data with custom operation
    const processedData = await dataProcessingManager.processUniversalData(
      operation,
      data,
      {
        threshold: 50, // Custom threshold
        appName: options.appName || 'universal',
        ...options
      }
    );
    
    console.log(`✅ Universal processing completed for ${operation}`);
    return processedData;
    
  } catch (error) {
    console.error(`❌ Universal processing failed for ${operation}:`, error);
    return data; // Return original data on error
  }
}

/**
 * Example 5: Filter Large Datasets
 */
export async function filterExample(documents, filters, appName = 'filter') {
  console.log(`🔍 [Filter Example] Filtering ${documents.length} documents...`);
  
  try {
    // Filter documents (uses web worker for >100 items)
    const filteredData = await dataProcessingManager.filterDocuments(
      documents,
      filters
    );
    
    console.log(`✅ Filtered to ${filteredData.length} documents`);
    return filteredData;
    
  } catch (error) {
    console.error(`❌ Filtering failed:`, error);
    return documents; // Return original data on error
  }
}

/**
 * Example 6: Sort Large Datasets
 */
export async function sortExample(documents, sortOptions, appName = 'sort') {
  console.log(`📈 [Sort Example] Sorting ${documents.length} documents...`);
  
  try {
    // Sort documents (uses web worker for >100 items)
    const sortedData = await dataProcessingManager.sortDocuments(
      documents,
      sortOptions
    );
    
    console.log(`✅ Sorted ${sortedData.length} documents`);
    return sortedData;
    
  } catch (error) {
    console.error(`❌ Sorting failed:`, error);
    return documents; // Return original data on error
  }
}

/**
 * Example 7: Calculate Statistics
 */
export async function statisticsExample(documents, fields, appName = 'stats') {
  console.log(`📊 [Statistics Example] Calculating stats for ${documents.length} documents...`);
  
  try {
    // Calculate statistics (always uses web worker if available)
    const stats = await dataProcessingManager.calculateStatistics(
      documents,
      fields
    );
    
    console.log(`✅ Statistics calculated for ${fields.length} fields`);
    console.log('📈 Results:', stats);
    return stats;
    
  } catch (error) {
    console.error(`❌ Statistics calculation failed:`, error);
    return {}; // Return empty stats on error
  }
}

/**
 * Example 8: Performance Comparison
 */
export async function performanceComparisonExample(data, operation = 'FILTER') {
  console.log(`⚡ [Performance Example] Comparing main thread vs web worker...`);
  
  const startTime = performance.now();
  
  try {
    // Process with web worker
    const result = await dataProcessingManager.processUniversalData(
      operation,
      data,
      { 
        appName: 'performance-test',
        threshold: 1 // Force web worker usage
      }
    );
    
    const endTime = performance.now();
    const processingTime = endTime - startTime;
    
    console.log(`✅ Web worker processing completed in ${processingTime.toFixed(2)}ms`);
    console.log(`📊 Processed ${Array.isArray(result) ? result.length : 1} items`);
    
    return {
      result,
      processingTime,
      method: 'web-worker'
    };
    
  } catch (error) {
    console.error(`❌ Performance test failed:`, error);
    return { error: error.message };
  }
}

/**
 * How to integrate into your app:
 * 
 * 1. Import the data processing manager:
 *    import dataProcessingManager from '../utils/DataProcessingManager.js';
 * 
 * 2. Use it in your data processing functions:
 *    const processedData = await dataProcessingManager.processTableData(
 *      yourTableData,
 *      yourRelatedData,
 *      { appName: 'your-app-name', threshold: 100 }
 *    );
 * 
 * 3. The system automatically:
 *    - Uses web workers for large datasets (above threshold)
 *    - Falls back to main thread for small datasets
 *    - Handles errors gracefully
 *    - Works across all apps (prosy, zenwrench, etc.)
 * 
 * 4. Benefits:
 *    - Non-blocking UI during heavy processing
 *    - Automatic performance optimization
 *    - Consistent API across all apps
 *    - Graceful fallbacks
 */

export default {
  prosyExample,
  zenWrenchExample,
  genericTableExample,
  universalExample,
  filterExample,
  sortExample,
  statisticsExample,
  performanceComparisonExample
};
