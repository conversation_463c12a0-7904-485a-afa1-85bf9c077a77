/**
 * PERFORMANCE FIX: Universal Data Processing Manager
 * Manages background data processing using web workers (PouchDB-safe)
 *
 * This manager handles CPU-intensive data operations in web workers
 * while keeping all PouchDB operations in the main thread to avoid
 * IndexedDB and browser API limitations in worker contexts.
 *
 * MODULAR DESIGN: Works with all apps (prosy, zenwrench, etc.)
 */

class DataProcessingManager {
  constructor() {
    this.worker = null;
    this.pendingOperations = new Map();
    this.operationId = 0;
    this.isInitialized = false;
    this.fallbackMode = false;
    this.appContext = null; // Track which app is using the manager
  }

  /**
   * Initialize the data processing worker
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if we're in a web worker environment and workers are supported
      if (typeof Worker !== 'undefined' && typeof window !== 'undefined') {
        this.worker = new Worker('/src/workers/databaseWorker.js');
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
        this.worker.onerror = this.handleWorkerError.bind(this);
        this.isInitialized = true;
        console.log('[DataProcessingManager] Worker initialized successfully');
      } else {
        console.warn('[DataProcessingManager] Web Workers not supported or not in browser context, using fallback mode');
        this.fallbackMode = true;
        this.isInitialized = true;
      }
    } catch (error) {
      console.error('[DataProcessingManager] Failed to initialize worker:', error);
      this.fallbackMode = true;
      this.isInitialized = true;
    }
  }

  /**
   * Handle messages from the worker
   */
  handleWorkerMessage(e) {
    const { type, id, success, results, documents, result, statistics, error } = e.data;
    const operation = this.pendingOperations.get(id);

    if (!operation) {
      console.warn('[DataProcessingManager] Received message for unknown operation:', id);
      return;
    }

    this.pendingOperations.delete(id);

    if (success) {
      switch (type) {
        case 'QUERY_RESULTS_PROCESSED':
          operation.resolve(results);
          break;
        case 'DOCUMENTS_FILTERED':
          operation.resolve(documents);
          break;
        case 'DOCUMENTS_SORTED':
          operation.resolve(documents);
          break;
        case 'DATA_AGGREGATED':
          operation.resolve(result);
          break;
        case 'DOCUMENTS_VALIDATED':
          operation.resolve(results);
          break;
        case 'DOCUMENTS_TRANSFORMED':
          operation.resolve(documents);
          break;
        case 'STATISTICS_CALCULATED':
          operation.resolve(statistics);
          break;
        case 'BUFF_UNITS_PROCESSED':
          operation.resolve(e.data.units);
          break;
        case 'BUFF_OCCUPATIONS_PROCESSED':
          operation.resolve(e.data.occupations);
          break;
        case 'UNIVERSAL_DATA_PROCESSED':
          operation.resolve(e.data.result);
          break;
        default:
          operation.reject(new Error(`Unknown response type: ${type}`));
      }
    } else {
      operation.reject(new Error(error));
    }
  }

  /**
   * Handle worker errors
   */
  handleWorkerError(error) {
    console.error('[DataProcessingManager] Worker error:', error);

    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('Worker error occurred'));
    }
    this.pendingOperations.clear();

    // Switch to fallback mode
    this.fallbackMode = true;
  }

  /**
   * Process query results with complex transformations
   */
  async processQueryResults(results, options = {}) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('PROCESS_QUERY_RESULTS', { results, options });
    } else {
      return this.processQueryResultsMainThread(results, options);
    }
  }

  /**
   * PERFORMANCE FIX: Universal data processing for any app
   */
  async processUniversalData(operation, data, options = {}) {
    await this.initialize();

    const { threshold = 100, appName = 'unknown' } = options;
    const dataSize = Array.isArray(data) ? data.length : Object.keys(data).length;

    if (this.worker && !this.fallbackMode && dataSize > threshold) {
      console.log(`[DataProcessingManager] Processing ${dataSize} items for ${appName} in web worker`);
      return this.processWithWorker('PROCESS_UNIVERSAL_DATA', {
        operation,
        data,
        options: { ...options, appName }
      });
    } else {
      return this.processUniversalDataMainThread(operation, data, options);
    }
  }

  /**
   * PERFORMANCE FIX: Process buffUnits data in web worker (Prosy-specific)
   */
  async processBuffUnits(unitsData, tenantsData, propertiesData, occupationsData, selectedBranch) {
    return this.processUniversalData('BUFF_UNITS', {
      unitsData,
      tenantsData,
      propertiesData,
      occupationsData,
      selectedBranch
    }, { threshold: 50, appName: 'prosy' });
  }

  /**
   * PERFORMANCE FIX: Process buffOccupations data in web worker (Prosy-specific)
   */
  async processBuffOccupations(occupationsData, tenantsData, unitsData, propertiesData, invoicesData, receiptsData, selectedBranch) {
    return this.processUniversalData('BUFF_OCCUPATIONS', {
      occupationsData,
      tenantsData,
      unitsData,
      propertiesData,
      invoicesData,
      receiptsData,
      selectedBranch
    }, { threshold: 100, appName: 'prosy' });
  }

  /**
   * PERFORMANCE FIX: Process ZenWrench invoices data
   */
  async processZenWrenchInvoices(invoicesData, customersData, servicesData, options = {}) {
    return this.processUniversalData('ZENWRENCH_INVOICES', {
      invoicesData,
      customersData,
      servicesData
    }, { threshold: 50, appName: 'zenwrench', ...options });
  }

  /**
   * PERFORMANCE FIX: Process ZenWrench work orders
   */
  async processZenWrenchWorkOrders(workOrdersData, customersData, techniciansData, options = {}) {
    return this.processUniversalData('ZENWRENCH_WORK_ORDERS', {
      workOrdersData,
      customersData,
      techniciansData
    }, { threshold: 75, appName: 'zenwrench', ...options });
  }

  /**
   * PERFORMANCE FIX: Generic table data processing for any app
   */
  async processTableData(tableData, relatedData = {}, options = {}) {
    const { appName = 'generic', operation = 'TABLE_PROCESSING' } = options;

    return this.processUniversalData(operation, {
      tableData,
      relatedData
    }, { threshold: 100, appName, ...options });
  }

  /**
   * Filter documents based on complex criteria
   */
  async filterDocuments(documents, filters) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('FILTER_DOCUMENTS', { documents, filters });
    } else {
      return this.filterDocumentsMainThread(documents, filters);
    }
  }

  /**
   * Sort documents by multiple criteria
   */
  async sortDocuments(documents, sortOptions) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('SORT_DOCUMENTS', { documents, sortOptions });
    } else {
      return this.sortDocumentsMainThread(documents, sortOptions);
    }
  }

  /**
   * Aggregate data with various operations
   */
  async aggregateData(documents, aggregation) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('AGGREGATE_DATA', { documents, aggregation });
    } else {
      return this.aggregateDataMainThread(documents, aggregation);
    }
  }

  /**
   * Calculate statistics for document fields
   */
  async calculateStatistics(documents, fields) {
    await this.initialize();

    if (this.worker && !this.fallbackMode) {
      return this.processWithWorker('CALCULATE_STATISTICS', { documents, fields });
    } else {
      return this.calculateStatisticsMainThread(documents, fields);
    }
  }

  /**
   * Generic worker processing method
   */
  async processWithWorker(type, data) {
    const id = ++this.operationId;

    return new Promise((resolve, reject) => {
      this.pendingOperations.set(id, { resolve, reject });

      this.worker.postMessage({
        type,
        id,
        data
      });

      // Set timeout to prevent hanging
      setTimeout(() => {
        if (this.pendingOperations.has(id)) {
          this.pendingOperations.delete(id);
          reject(new Error(`${type} processing timeout`));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * PERFORMANCE FIX: Universal main thread processing fallback
   */
  async processUniversalDataMainThread(operation, data, options = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          console.log(`[DataProcessingManager] Processing ${operation} on main thread`);

          switch (operation) {
            case 'BUFF_UNITS':
              resolve(this.processBuffUnitsMainThread(
                data.unitsData,
                data.tenantsData,
                data.propertiesData,
                data.occupationsData,
                data.selectedBranch
              ));
              break;

            case 'BUFF_OCCUPATIONS':
              resolve(this.processBuffOccupationsMainThread(
                data.occupationsData,
                data.tenantsData,
                data.unitsData,
                data.propertiesData,
                data.invoicesData,
                data.receiptsData,
                data.selectedBranch
              ));
              break;

            case 'ZENWRENCH_INVOICES':
              resolve(this.processZenWrenchInvoicesMainThread(data, options));
              break;

            case 'ZENWRENCH_WORK_ORDERS':
              resolve(this.processZenWrenchWorkOrdersMainThread(data, options));
              break;

            case 'TABLE_PROCESSING':
              resolve(this.processTableDataMainThread(data, options));
              break;

            default:
              // Generic processing
              resolve({
                processed: true,
                operation,
                data: Array.isArray(data) ? data : [data],
                processedAt: new Date().toISOString(),
                appName: options.appName || 'unknown'
              });
          }
        } catch (error) {
          console.error(`[DataProcessingManager] Main thread processing failed for ${operation}:`, error);
          resolve({ error: error.message, operation, data });
        }
      }, 0);
    });
  }

  /**
   * Fallback: Process query results on main thread
   */
  async processQueryResultsMainThread(results, options = {}) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simple processing for fallback
        const processedResults = {
          results: results,
          metadata: options.includeMetadata ? { processedAt: new Date().toISOString() } : undefined,
          stats: options.calculateStats ? { totalCount: results.length, processedCount: results.length } : undefined
        };
        resolve(processedResults);
      }, 0);
    });
  }

  /**
   * Fallback: Filter documents on main thread
   */
  async filterDocumentsMainThread(documents, filters) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!filters || Object.keys(filters).length === 0) {
          resolve(documents);
          return;
        }

        const filtered = documents.filter(doc => {
          return Object.entries(filters).every(([field, criteria]) => {
            const value = this.getNestedValue(doc, field);
            return value === criteria; // Simple equality check for fallback
          });
        });

        resolve(filtered);
      }, 0);
    });
  }

  /**
   * Fallback: Sort documents on main thread
   */
  async sortDocumentsMainThread(documents, sortOptions) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!sortOptions || sortOptions.length === 0) {
          resolve(documents);
          return;
        }

        const sorted = [...documents].sort((a, b) => {
          const { field, direction = 'asc' } = sortOptions[0]; // Use first sort option only for fallback
          const aValue = this.getNestedValue(a, field);
          const bValue = this.getNestedValue(b, field);

          if (aValue < bValue) return direction === 'desc' ? 1 : -1;
          if (aValue > bValue) return direction === 'desc' ? -1 : 1;
          return 0;
        });

        resolve(sorted);
      }, 0);
    });
  }

  /**
   * Fallback: Aggregate data on main thread
   */
  async aggregateDataMainThread(documents, aggregation) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simple count aggregation for fallback
        const result = {
          count: documents.length,
          processed: true
        };
        resolve(result);
      }, 0);
    });
  }

  /**
   * Fallback: Calculate statistics on main thread
   */
  async calculateStatisticsMainThread(documents, fields) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stats = {};
        fields.forEach(field => {
          const values = documents.map(doc => this.getNestedValue(doc, field)).filter(v => v !== undefined);
          stats[field] = {
            count: values.length,
            uniqueCount: new Set(values).size,
            type: 'mixed'
          };
        });
        resolve(stats);
      }, 0);
    });
  }

  /**
   * PERFORMANCE FIX: Fallback buffUnits processing on main thread
   */
  async processBuffUnitsMainThread(unitsData, tenantsData, propertiesData, occupationsData, selectedBranch) {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          // Simplified buffUnits logic for main thread fallback
          const processedUnits = unitsData.map(unit => {
            // Find related data
            const property = propertiesData.find(p => p._id === unit.property_id);
            const activeOccupations = occupationsData.filter(o => o.unit === unit._id && !o.dateOut);

            return {
              ...unit,
              property: property || {},
              occupations: activeOccupations,
              occupancyStatus: activeOccupations.length > 0 ? 'occupied' : 'vacant',
              occupantCount: activeOccupations.length
            };
          });

          resolve(processedUnits);
        } catch (error) {
          console.error('[DataProcessingManager] BuffUnits main thread error:', error);
          resolve(unitsData); // Return original data on error
        }
      }, 0);
    });
  }

  /**
   * PERFORMANCE FIX: Fallback buffOccupations processing on main thread
   */
  async processBuffOccupationsMainThread(occupationsData, tenantsData, unitsData, propertiesData, invoicesData, receiptsData, selectedBranch) {
    return new Promise((resolve) => {
      setTimeout(() => {
        try {
          // Simplified buffOccupations logic for main thread fallback
          const processedOccupations = occupationsData.map(occupation => {
            // Find related data
            const tenant = tenantsData.find(t => t._id === occupation.tenant);
            const unit = unitsData.find(u => u._id === occupation.unit);
            const property = propertiesData.find(p => p._id === unit?.property_id);

            // Calculate financial data
            const relatedInvoices = invoicesData.filter(i => i.tenant === occupation._id);
            const relatedReceipts = receiptsData.filter(r => r.tenant === occupation._id);

            const totalRent = relatedInvoices.reduce((sum, inv) => sum + (inv.amount || 0), 0);
            const totalPaid = relatedReceipts.reduce((sum, rec) => sum + (rec.amount || 0), 0);
            const balance = totalRent - totalPaid;

            return {
              ...occupation,
              tenant: tenant || {},
              unit: unit || {},
              property: property || {},
              financials: {
                totalRent,
                totalPaid,
                balance,
                status: balance > 0 ? 'arrears' : balance < 0 ? 'advance' : 'current'
              }
            };
          });

          resolve(processedOccupations);
        } catch (error) {
          console.error('[DataProcessingManager] BuffOccupations main thread error:', error);
          resolve(occupationsData); // Return original data on error
        }
      }, 0);
    });
  }

  /**
   * PERFORMANCE FIX: ZenWrench invoices main thread processing
   */
  async processZenWrenchInvoicesMainThread(data, options = {}) {
    const { invoicesData, customersData, servicesData } = data;

    return invoicesData.map(invoice => {
      const customer = customersData.find(c => c._id === invoice.customerId) || {};
      const services = servicesData.filter(s => s.invoiceId === invoice._id);

      const totalAmount = services.reduce((sum, service) => sum + (service.amount || 0), 0);

      return {
        ...invoice,
        customer,
        services,
        totalAmount,
        status: invoice.paid ? 'paid' : totalAmount > 0 ? 'pending' : 'draft'
      };
    });
  }

  /**
   * PERFORMANCE FIX: ZenWrench work orders main thread processing
   */
  async processZenWrenchWorkOrdersMainThread(data, options = {}) {
    const { workOrdersData, customersData, techniciansData } = data;

    return workOrdersData.map(workOrder => {
      const customer = customersData.find(c => c._id === workOrder.customerId) || {};
      const technician = techniciansData.find(t => t._id === workOrder.technicianId) || {};

      return {
        ...workOrder,
        customer,
        technician,
        displayName: `${workOrder.number || workOrder._id} - ${customer.name || 'Unknown Customer'}`,
        statusDisplay: workOrder.status || 'pending'
      };
    });
  }

  /**
   * PERFORMANCE FIX: Generic table data main thread processing
   */
  async processTableDataMainThread(data, options = {}) {
    const { tableData, relatedData } = data;

    if (!Array.isArray(tableData)) {
      return tableData;
    }

    return tableData.map(row => ({
      ...row,
      processed: true,
      processedAt: new Date().toISOString(),
      relatedDataCount: Object.keys(relatedData).length
    }));
  }

  /**
   * Get nested value from object using dot notation
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // Reject all pending operations
    for (const [id, operation] of this.pendingOperations) {
      operation.reject(new Error('DataProcessingManager destroyed'));
    }
    this.pendingOperations.clear();
    this.isInitialized = false;
    this.fallbackMode = false;
  }
}

// Create singleton instance
const dataProcessingManager = new DataProcessingManager();

export default dataProcessingManager;
