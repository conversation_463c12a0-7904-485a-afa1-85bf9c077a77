/**
 * PERFORMANCE FIX: Web Worker Performance Demo
 * Demonstrates how to use web workers for CPU-intensive operations
 * without interfering with PouchDB operations
 */

import dataProcessingManager from './DataProcessingManager.js';

/**
 * Demo function showing performance improvement with web workers
 */
export async function demonstrateWebWorkerPerformance() {
  console.log('🚀 [WebWorkerDemo] Starting performance demonstration...');

  // Generate sample data (simulating database results)
  const sampleUnits = generateSampleUnits(1000);
  const sampleTenants = generateSampleTenants(500);
  const sampleProperties = generateSampleProperties(100);
  const sampleOccupations = generateSampleOccupations(800);

  console.log(`📊 Generated sample data:
    - ${sampleUnits.length} units
    - ${sampleTenants.length} tenants  
    - ${sampleProperties.length} properties
    - ${sampleOccupations.length} occupations`);

  // Test 1: Process units with web worker
  console.log('\n🔧 Test 1: Processing units...');
  const startTime1 = performance.now();
  
  try {
    const processedUnits = await dataProcessingManager.processBuffUnits(
      sampleUnits,
      sampleTenants,
      sampleProperties,
      sampleOccupations,
      'all'
    );
    
    const endTime1 = performance.now();
    console.log(`✅ Units processed successfully in ${(endTime1 - startTime1).toFixed(2)}ms`);
    console.log(`📈 Processed ${processedUnits.length} units with enhanced data`);
  } catch (error) {
    console.error('❌ Units processing failed:', error);
  }

  // Test 2: Process occupations with web worker
  console.log('\n🔧 Test 2: Processing occupations...');
  const startTime2 = performance.now();
  
  try {
    const processedOccupations = await dataProcessingManager.processBuffOccupations(
      sampleOccupations,
      sampleTenants,
      sampleUnits,
      sampleProperties,
      [], // invoices
      [], // receipts
      'all'
    );
    
    const endTime2 = performance.now();
    console.log(`✅ Occupations processed successfully in ${(endTime2 - startTime2).toFixed(2)}ms`);
    console.log(`📈 Processed ${processedOccupations.length} occupations with financial data`);
  } catch (error) {
    console.error('❌ Occupations processing failed:', error);
  }

  // Test 3: Filter large dataset
  console.log('\n🔧 Test 3: Filtering large dataset...');
  const startTime3 = performance.now();
  
  try {
    const filteredData = await dataProcessingManager.filterDocuments(
      sampleUnits,
      { occupancyStatus: 'occupied' }
    );
    
    const endTime3 = performance.now();
    console.log(`✅ Filtering completed in ${(endTime3 - startTime3).toFixed(2)}ms`);
    console.log(`📈 Filtered to ${filteredData.length} occupied units`);
  } catch (error) {
    console.error('❌ Filtering failed:', error);
  }

  // Test 4: Calculate statistics
  console.log('\n🔧 Test 4: Calculating statistics...');
  const startTime4 = performance.now();
  
  try {
    const stats = await dataProcessingManager.calculateStatistics(
      sampleUnits,
      ['rent', 'size', 'occupancyStatus']
    );
    
    const endTime4 = performance.now();
    console.log(`✅ Statistics calculated in ${(endTime4 - startTime4).toFixed(2)}ms`);
    console.log('📊 Statistics:', stats);
  } catch (error) {
    console.error('❌ Statistics calculation failed:', error);
  }

  console.log('\n🎉 Web worker performance demonstration completed!');
}

/**
 * Generate sample units data
 */
function generateSampleUnits(count) {
  const units = [];
  for (let i = 0; i < count; i++) {
    units.push({
      _id: `unit_${i}`,
      name: `Unit ${i + 1}`,
      unitNumber: `${Math.floor(i / 10) + 1}${String.fromCharCode(65 + (i % 10))}`,
      property_id: `property_${Math.floor(i / 10)}`,
      rent: Math.floor(Math.random() * 2000) + 500,
      size: Math.floor(Math.random() * 1000) + 400,
      bedrooms: Math.floor(Math.random() * 4) + 1,
      bathrooms: Math.floor(Math.random() * 3) + 1,
      occupancyStatus: Math.random() > 0.3 ? 'occupied' : 'vacant',
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  return units;
}

/**
 * Generate sample tenants data
 */
function generateSampleTenants(count) {
  const tenants = [];
  const firstNames = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Lisa', 'Chris', 'Emma'];
  const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
  
  for (let i = 0; i < count; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    
    tenants.push({
      _id: `tenant_${i}`,
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@email.com`,
      phone: `555-${String(Math.floor(Math.random() * 9000) + 1000)}`,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  return tenants;
}

/**
 * Generate sample properties data
 */
function generateSampleProperties(count) {
  const properties = [];
  const propertyNames = ['Sunset Apartments', 'Oak Grove Complex', 'Riverside Towers', 'Garden View', 'City Center'];
  
  for (let i = 0; i < count; i++) {
    properties.push({
      _id: `property_${i}`,
      name: `${propertyNames[i % propertyNames.length]} ${Math.floor(i / propertyNames.length) + 1}`,
      address: `${Math.floor(Math.random() * 9999) + 1} Main St`,
      city: 'Sample City',
      state: 'SC',
      zipCode: `${Math.floor(Math.random() * 90000) + 10000}`,
      units: Math.floor(Math.random() * 50) + 10,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  return properties;
}

/**
 * Generate sample occupations data
 */
function generateSampleOccupations(count) {
  const occupations = [];
  
  for (let i = 0; i < count; i++) {
    const dateIn = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    const isActive = Math.random() > 0.2; // 80% active occupations
    
    occupations.push({
      _id: `occupation_${i}`,
      tenant: `tenant_${Math.floor(Math.random() * 500)}`,
      unit: `unit_${Math.floor(Math.random() * 1000)}`,
      dateIn: dateIn.toISOString(),
      dateOut: isActive ? null : new Date(dateIn.getTime() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString(),
      rent: Math.floor(Math.random() * 2000) + 500,
      deposit: Math.floor(Math.random() * 1000) + 200,
      status: isActive ? 'active' : 'terminated',
      createdAt: dateIn.toISOString()
    });
  }
  return occupations;
}

/**
 * Benchmark function to compare main thread vs web worker performance
 */
export async function benchmarkPerformance(dataSize = 1000) {
  console.log(`🏁 [Benchmark] Starting performance comparison with ${dataSize} records...`);
  
  const sampleData = generateSampleUnits(dataSize);
  
  // Test main thread processing
  console.log('🔧 Testing main thread processing...');
  const mainThreadStart = performance.now();
  
  // Simulate CPU-intensive operation on main thread
  const mainThreadResult = sampleData.map(unit => ({
    ...unit,
    processed: true,
    computedValue: Math.sqrt(unit.rent * unit.size),
    category: unit.rent > 1000 ? 'premium' : 'standard'
  }));
  
  const mainThreadEnd = performance.now();
  const mainThreadTime = mainThreadEnd - mainThreadStart;
  
  // Test web worker processing
  console.log('🔧 Testing web worker processing...');
  const workerStart = performance.now();
  
  try {
    const workerResult = await dataProcessingManager.processQueryResults(sampleData, {
      includeMetadata: true,
      calculateStats: true
    });
    
    const workerEnd = performance.now();
    const workerTime = workerEnd - workerStart;
    
    console.log(`📊 Performance Results:
      Main Thread: ${mainThreadTime.toFixed(2)}ms (${mainThreadResult.length} records)
      Web Worker: ${workerTime.toFixed(2)}ms (${workerResult.results.length} records)
      Improvement: ${((mainThreadTime - workerTime) / mainThreadTime * 100).toFixed(1)}%`);
      
  } catch (error) {
    console.error('❌ Web worker benchmark failed:', error);
  }
}

export default {
  demonstrateWebWorkerPerformance,
  benchmarkPerformance
};
