/**
 * PERFORMANCE FIX: Database Worker for CPU-intensive operations
 * Handles heavy data processing operations in background to prevent UI blocking
 *
 * IMPORTANT: This worker does NOT interact with PouchDB directly to avoid
 * IndexedDB and browser API limitations in worker contexts. It only processes
 * plain JavaScript data that has already been retrieved from the database.
 */

// Data processing worker - NO PouchDB operations
self.onmessage = async function (e) {
  const { type, data, id } = e.data;

  try {
    switch (type) {
      case 'PROCESS_QUERY_RESULTS':
        const processedResults = await processQueryResults(data.results, data.options);
        self.postMessage({
          type: 'QUERY_RESULTS_PROCESSED',
          id,
          success: true,
          results: processedResults
        });
        break;

      case 'FILTER_DOCUMENTS':
        const filteredDocs = await filterDocuments(data.documents, data.filters);
        self.postMessage({
          type: 'DOCUMENTS_FILTERED',
          id,
          success: true,
          documents: filteredDocs
        });
        break;

      case 'SORT_DOCUMENTS':
        const sortedDocs = await sortDocuments(data.documents, data.sortOptions);
        self.postMessage({
          type: 'DOCUMENTS_SORTED',
          id,
          success: true,
          documents: sortedDocs
        });
        break;

      case 'AGGREGATE_DATA':
        const aggregatedData = await aggregateData(data.documents, data.aggregation);
        self.postMessage({
          type: 'DATA_AGGREGATED',
          id,
          success: true,
          result: aggregatedData
        });
        break;

      case 'VALIDATE_DOCUMENTS':
        const validationResults = await validateDocuments(data.documents, data.schema);
        self.postMessage({
          type: 'DOCUMENTS_VALIDATED',
          id,
          success: true,
          results: validationResults
        });
        break;

      case 'TRANSFORM_DOCUMENTS':
        const transformedDocs = await transformDocuments(data.documents, data.transformer);
        self.postMessage({
          type: 'DOCUMENTS_TRANSFORMED',
          id,
          success: true,
          documents: transformedDocs
        });
        break;

      case 'CALCULATE_STATISTICS':
        const stats = await calculateStatistics(data.documents, data.fields);
        self.postMessage({
          type: 'STATISTICS_CALCULATED',
          id,
          success: true,
          statistics: stats
        });
        break;

      case 'PROCESS_BUFF_UNITS':
        const buffedUnits = await processBuffUnits(
          data.unitsData,
          data.tenantsData,
          data.propertiesData,
          data.occupationsData,
          data.selectedBranch
        );
        self.postMessage({
          type: 'BUFF_UNITS_PROCESSED',
          id,
          success: true,
          units: buffedUnits
        });
        break;

      case 'PROCESS_BUFF_OCCUPATIONS':
        const buffedOccupations = await processBuffOccupations(
          data.occupationsData,
          data.tenantsData,
          data.unitsData,
          data.propertiesData,
          data.invoicesData,
          data.receiptsData,
          data.selectedBranch
        );
        self.postMessage({
          type: 'BUFF_OCCUPATIONS_PROCESSED',
          id,
          success: true,
          occupations: buffedOccupations
        });
        break;

      case 'PROCESS_UNIVERSAL_DATA':
        const universalResult = await processUniversalData(data.operation, data.data, data.options);
        self.postMessage({
          type: 'UNIVERSAL_DATA_PROCESSED',
          id,
          success: true,
          result: universalResult
        });
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      success: false,
      error: error.message
    });
  }
};

/**
 * Process query results with complex transformations
 */
async function processQueryResults(results, options = {}) {
  const {
    includeMetadata = false,
    calculateStats = false,
    groupBy = null,
    transform = null
  } = options;

  let processedResults = [...results];

  // Apply transformations
  if (transform && typeof transform === 'function') {
    processedResults = processedResults.map(transform);
  }

  // Group by field if specified
  if (groupBy) {
    const grouped = {};
    processedResults.forEach(doc => {
      const key = doc[groupBy] || 'undefined';
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(doc);
    });
    processedResults = grouped;
  }

  // Calculate statistics if requested
  let stats = null;
  if (calculateStats) {
    stats = {
      totalCount: results.length,
      processedCount: Array.isArray(processedResults) ? processedResults.length : Object.keys(processedResults).length,
      processingTime: Date.now()
    };
  }

  return {
    results: processedResults,
    ...(includeMetadata && { metadata: { processedAt: new Date().toISOString() } }),
    ...(stats && { stats })
  };
}

/**
 * Filter documents based on complex criteria
 */
async function filterDocuments(documents, filters) {
  if (!filters || Object.keys(filters).length === 0) {
    return documents;
  }

  return documents.filter(doc => {
    return Object.entries(filters).every(([field, criteria]) => {
      const value = getNestedValue(doc, field);

      if (typeof criteria === 'object' && criteria !== null) {
        // Handle complex criteria
        if (criteria.$eq !== undefined) return value === criteria.$eq;
        if (criteria.$ne !== undefined) return value !== criteria.$ne;
        if (criteria.$gt !== undefined) return value > criteria.$gt;
        if (criteria.$gte !== undefined) return value >= criteria.$gte;
        if (criteria.$lt !== undefined) return value < criteria.$lt;
        if (criteria.$lte !== undefined) return value <= criteria.$lte;
        if (criteria.$in !== undefined) return criteria.$in.includes(value);
        if (criteria.$nin !== undefined) return !criteria.$nin.includes(value);
        if (criteria.$regex !== undefined) {
          const regex = new RegExp(criteria.$regex, criteria.$options || '');
          return regex.test(String(value));
        }
        if (criteria.$exists !== undefined) {
          return criteria.$exists ? (value !== undefined) : (value === undefined);
        }
      } else {
        // Simple equality check
        return value === criteria;
      }

      return true;
    });
  });
}

/**
 * Sort documents by multiple criteria
 */
async function sortDocuments(documents, sortOptions) {
  if (!sortOptions || sortOptions.length === 0) {
    return documents;
  }

  return documents.sort((a, b) => {
    for (const { field, direction = 'asc' } of sortOptions) {
      const aValue = getNestedValue(a, field);
      const bValue = getNestedValue(b, field);

      let comparison = 0;

      if (aValue < bValue) comparison = -1;
      else if (aValue > bValue) comparison = 1;

      if (comparison !== 0) {
        return direction === 'desc' ? -comparison : comparison;
      }
    }
    return 0;
  });
}

/**
 * Aggregate data with various operations
 */
async function aggregateData(documents, aggregation) {
  const { groupBy, operations = [] } = aggregation;

  if (!groupBy) {
    // Simple aggregation without grouping
    const result = {};
    operations.forEach(op => {
      result[op.name] = performAggregation(documents, op);
    });
    return result;
  }

  // Group documents first
  const groups = {};
  documents.forEach(doc => {
    const key = getNestedValue(doc, groupBy);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(doc);
  });

  // Apply aggregations to each group
  const result = {};
  Object.entries(groups).forEach(([key, groupDocs]) => {
    result[key] = {};
    operations.forEach(op => {
      result[key][op.name] = performAggregation(groupDocs, op);
    });
  });

  return result;
}

/**
 * Perform individual aggregation operation
 */
function performAggregation(documents, operation) {
  const { type, field } = operation;

  switch (type) {
    case 'count':
      return documents.length;

    case 'sum':
      return documents.reduce((sum, doc) => {
        const value = getNestedValue(doc, field);
        return sum + (typeof value === 'number' ? value : 0);
      }, 0);

    case 'avg':
      const sum = documents.reduce((sum, doc) => {
        const value = getNestedValue(doc, field);
        return sum + (typeof value === 'number' ? value : 0);
      }, 0);
      return documents.length > 0 ? sum / documents.length : 0;

    case 'min':
      return documents.reduce((min, doc) => {
        const value = getNestedValue(doc, field);
        return typeof value === 'number' && (min === null || value < min) ? value : min;
      }, null);

    case 'max':
      return documents.reduce((max, doc) => {
        const value = getNestedValue(doc, field);
        return typeof value === 'number' && (max === null || value > max) ? value : max;
      }, null);

    default:
      return null;
  }
}

/**
 * Validate documents against schema
 */
async function validateDocuments(documents, schema) {
  return documents.map(doc => {
    const errors = [];

    // Check required fields
    if (schema.required) {
      schema.required.forEach(field => {
        if (getNestedValue(doc, field) === undefined) {
          errors.push(`Missing required field: ${field}`);
        }
      });
    }

    // Check field types
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([field, fieldSchema]) => {
        const value = getNestedValue(doc, field);
        if (value !== undefined && fieldSchema.type) {
          const actualType = Array.isArray(value) ? 'array' : typeof value;
          if (actualType !== fieldSchema.type) {
            errors.push(`Field ${field} should be ${fieldSchema.type}, got ${actualType}`);
          }
        }
      });
    }

    return {
      document: doc,
      valid: errors.length === 0,
      errors
    };
  });
}

/**
 * Transform documents using provided transformer function
 */
async function transformDocuments(documents, transformer) {
  if (typeof transformer !== 'function') {
    return documents;
  }

  return documents.map((doc, index) => {
    try {
      return transformer(doc, index);
    } catch (error) {
      console.warn('Document transformation failed:', error);
      return doc; // Return original document if transformation fails
    }
  });
}

/**
 * Calculate statistics for document fields
 */
async function calculateStatistics(documents, fields) {
  const stats = {};

  fields.forEach(field => {
    const values = documents
      .map(doc => getNestedValue(doc, field))
      .filter(value => value !== undefined && value !== null);

    if (values.length === 0) {
      stats[field] = { count: 0, type: 'empty' };
      return;
    }

    const numericValues = values.filter(v => typeof v === 'number');
    const stringValues = values.filter(v => typeof v === 'string');
    const dateValues = values.filter(v => v instanceof Date || (typeof v === 'string' && !isNaN(Date.parse(v))));

    stats[field] = {
      count: values.length,
      uniqueCount: new Set(values).size,
      type: numericValues.length > 0 ? 'numeric' : stringValues.length > 0 ? 'string' : 'mixed'
    };

    // Numeric statistics
    if (numericValues.length > 0) {
      const sorted = numericValues.sort((a, b) => a - b);
      stats[field].numeric = {
        min: sorted[0],
        max: sorted[sorted.length - 1],
        sum: numericValues.reduce((sum, val) => sum + val, 0),
        avg: numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length,
        median: sorted[Math.floor(sorted.length / 2)]
      };
    }

    // String statistics
    if (stringValues.length > 0) {
      const lengths = stringValues.map(s => s.length);
      stats[field].string = {
        minLength: Math.min(...lengths),
        maxLength: Math.max(...lengths),
        avgLength: lengths.reduce((sum, len) => sum + len, 0) / lengths.length,
        mostCommon: getMostCommonValue(stringValues)
      };
    }

    // Date statistics
    if (dateValues.length > 0) {
      const dates = dateValues.map(d => new Date(d)).filter(d => !isNaN(d));
      if (dates.length > 0) {
        const sortedDates = dates.sort((a, b) => a - b);
        stats[field].date = {
          earliest: sortedDates[0],
          latest: sortedDates[sortedDates.length - 1],
          range: sortedDates[sortedDates.length - 1] - sortedDates[0]
        };
      }
    }
  });

  return stats;
}

/**
 * Get most common value in array
 */
function getMostCommonValue(values) {
  const counts = {};
  values.forEach(value => {
    counts[value] = (counts[value] || 0) + 1;
  });

  let mostCommon = null;
  let maxCount = 0;

  Object.entries(counts).forEach(([value, count]) => {
    if (count > maxCount) {
      maxCount = count;
      mostCommon = value;
    }
  });

  return { value: mostCommon, count: maxCount };
}

/**
 * PERFORMANCE FIX: Process buffUnits data in web worker
 */
async function processBuffUnits(unitsData, tenantsData, propertiesData, occupationsData, selectedBranch) {
  console.log(`[Worker] Processing ${unitsData.length} units`);

  // Pre-filter active occupations for better performance
  const activeOccupations = occupationsData.filter(o => !o.dateOut);

  // Create lookup maps for faster access
  const propertyMap = new Map(propertiesData.map(p => [p._id, p]));
  const tenantMap = new Map(tenantsData.map(t => [t._id, t]));

  const processedUnits = unitsData.map(unit => {
    try {
      // Find related data using maps for O(1) lookup
      const property = propertyMap.get(unit.property_id) || {};
      const unitOccupations = activeOccupations.filter(o => o.unit === unit._id);

      // Calculate occupancy details
      const occupancyStatus = unitOccupations.length > 0 ? 'occupied' : 'vacant';
      const occupantCount = unitOccupations.length;

      // Get tenant details for occupied units
      const tenants = unitOccupations.map(occ => {
        const tenant = tenantMap.get(occ.tenant) || {};
        return {
          ...tenant,
          occupationId: occ._id,
          dateIn: occ.dateIn,
          rent: occ.rent || 0
        };
      });

      // Calculate total rent for the unit
      const totalRent = unitOccupations.reduce((sum, occ) => sum + (occ.rent || 0), 0);

      return {
        ...unit,
        property,
        occupations: unitOccupations,
        tenants,
        occupancyStatus,
        occupantCount,
        totalRent,
        // Additional computed fields
        isVacant: occupancyStatus === 'vacant',
        isOccupied: occupancyStatus === 'occupied',
        propertyName: property.name || 'Unknown Property',
        unitDisplay: `${property.name || 'Unknown'} - ${unit.name || unit.unitNumber || 'Unit'}`
      };
    } catch (error) {
      console.warn('[Worker] Error processing unit:', unit._id, error);
      return unit; // Return original unit on error
    }
  });

  console.log(`[Worker] Processed ${processedUnits.length} units successfully`);
  return processedUnits;
}

/**
 * PERFORMANCE FIX: Process buffOccupations data in web worker
 */
async function processBuffOccupations(occupationsData, tenantsData, unitsData, propertiesData, invoicesData, receiptsData, selectedBranch) {
  console.log(`[Worker] Processing ${occupationsData.length} occupations`);

  // Create lookup maps for O(1) access
  const tenantMap = new Map(tenantsData.map(t => [t._id, t]));
  const unitMap = new Map(unitsData.map(u => [u._id, u]));
  const propertyMap = new Map(propertiesData.map(p => [p._id, p]));

  // Group invoices and receipts by tenant for faster lookup
  const invoicesByTenant = new Map();
  const receiptsByTenant = new Map();

  invoicesData.forEach(invoice => {
    if (!invoicesByTenant.has(invoice.tenant)) {
      invoicesByTenant.set(invoice.tenant, []);
    }
    invoicesByTenant.get(invoice.tenant).push(invoice);
  });

  receiptsData.forEach(receipt => {
    if (!receiptsByTenant.has(receipt.tenant)) {
      receiptsByTenant.set(receipt.tenant, []);
    }
    receiptsByTenant.get(receipt.tenant).push(receipt);
  });

  const processedOccupations = occupationsData.map(occupation => {
    try {
      // Get related data using maps
      const tenant = tenantMap.get(occupation.tenant) || {};
      const unit = unitMap.get(occupation.unit) || {};
      const property = propertyMap.get(unit.property_id) || {};

      // Get financial data
      const relatedInvoices = invoicesByTenant.get(occupation._id) || [];
      const relatedReceipts = receiptsByTenant.get(occupation._id) || [];

      // Calculate financial totals
      const totalRent = relatedInvoices.reduce((sum, inv) => sum + (parseFloat(inv.amount) || 0), 0);
      const totalPaid = relatedReceipts.reduce((sum, rec) => sum + (parseFloat(rec.amount) || 0), 0);
      const balance = totalRent - totalPaid;

      // Determine financial status
      let financialStatus = 'current';
      if (balance > 0) financialStatus = 'arrears';
      else if (balance < 0) financialStatus = 'advance';

      // Calculate occupancy duration
      const dateIn = new Date(occupation.dateIn);
      const dateOut = occupation.dateOut ? new Date(occupation.dateOut) : new Date();
      const occupancyDays = Math.floor((dateOut - dateIn) / (1000 * 60 * 60 * 24));

      return {
        ...occupation,
        tenant: {
          ...tenant,
          displayName: tenant.name || `${tenant.firstName || ''} ${tenant.lastName || ''}`.trim() || 'Unknown Tenant'
        },
        unit: {
          ...unit,
          displayName: unit.name || unit.unitNumber || 'Unknown Unit'
        },
        property: {
          ...property,
          displayName: property.name || 'Unknown Property'
        },
        financials: {
          totalRent: Math.round(totalRent * 100) / 100, // Round to 2 decimal places
          totalPaid: Math.round(totalPaid * 100) / 100,
          balance: Math.round(balance * 100) / 100,
          status: financialStatus,
          invoiceCount: relatedInvoices.length,
          receiptCount: relatedReceipts.length
        },
        occupancy: {
          days: occupancyDays,
          months: Math.round(occupancyDays / 30 * 10) / 10, // Round to 1 decimal
          isActive: !occupation.dateOut,
          rent: parseFloat(occupation.rent) || 0
        },
        // Display fields for UI
        displayInfo: {
          tenantName: tenant.name || `${tenant.firstName || ''} ${tenant.lastName || ''}`.trim() || 'Unknown',
          unitName: `${property.name || 'Unknown'} - ${unit.name || unit.unitNumber || 'Unit'}`,
          propertyName: property.name || 'Unknown Property',
          rentAmount: `$${(parseFloat(occupation.rent) || 0).toFixed(2)}`,
          balanceAmount: `$${Math.abs(balance).toFixed(2)}`,
          balanceStatus: financialStatus
        }
      };
    } catch (error) {
      console.warn('[Worker] Error processing occupation:', occupation._id, error);
      return occupation; // Return original occupation on error
    }
  });

  console.log(`[Worker] Processed ${processedOccupations.length} occupations successfully`);
  return processedOccupations;
}

/**
 * PERFORMANCE FIX: Universal data processing for all apps
 */
async function processUniversalData(operation, data, options = {}) {
  console.log(`[Worker] Processing universal operation: ${operation} for app: ${options.appName || 'unknown'}`);

  try {
    switch (operation) {
      case 'BUFF_UNITS':
        return await processBuffUnits(
          data.unitsData,
          data.tenantsData,
          data.propertiesData,
          data.occupationsData,
          data.selectedBranch
        );

      case 'BUFF_OCCUPATIONS':
        return await processBuffOccupations(
          data.occupationsData,
          data.tenantsData,
          data.unitsData,
          data.propertiesData,
          data.invoicesData,
          data.receiptsData,
          data.selectedBranch
        );

      case 'ZENWRENCH_INVOICES':
        return await processZenWrenchInvoices(data.invoicesData, data.customersData, data.servicesData);

      case 'ZENWRENCH_WORK_ORDERS':
        return await processZenWrenchWorkOrders(data.workOrdersData, data.customersData, data.techniciansData);

      case 'TABLE_PROCESSING':
        return await processTableData(data.tableData, data.relatedData, options);

      default:
        // Generic processing for unknown operations
        return {
          processed: true,
          operation,
          data: Array.isArray(data) ? data : [data],
          processedAt: new Date().toISOString(),
          appName: options.appName || 'unknown'
        };
    }
  } catch (error) {
    console.error(`[Worker] Universal processing failed for ${operation}:`, error);
    throw error;
  }
}

/**
 * PERFORMANCE FIX: Process ZenWrench invoices in web worker
 */
async function processZenWrenchInvoices(invoicesData, customersData, servicesData) {
  console.log(`[Worker] Processing ${invoicesData.length} ZenWrench invoices`);

  const customerMap = new Map(customersData.map(c => [c._id, c]));
  const servicesByInvoice = new Map();

  // Group services by invoice
  servicesData.forEach(service => {
    if (!servicesByInvoice.has(service.invoiceId)) {
      servicesByInvoice.set(service.invoiceId, []);
    }
    servicesByInvoice.get(service.invoiceId).push(service);
  });

  return invoicesData.map(invoice => {
    const customer = customerMap.get(invoice.customerId) || {};
    const services = servicesByInvoice.get(invoice._id) || [];
    const totalAmount = services.reduce((sum, service) => sum + (parseFloat(service.amount) || 0), 0);

    return {
      ...invoice,
      customer: {
        ...customer,
        displayName: customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'Unknown Customer'
      },
      services,
      financials: {
        totalAmount: Math.round(totalAmount * 100) / 100,
        serviceCount: services.length,
        status: invoice.paid ? 'paid' : totalAmount > 0 ? 'pending' : 'draft'
      },
      displayInfo: {
        customerName: customer.name || 'Unknown Customer',
        totalAmount: `$${totalAmount.toFixed(2)}`,
        serviceCount: services.length
      }
    };
  });
}

/**
 * PERFORMANCE FIX: Process ZenWrench work orders in web worker
 */
async function processZenWrenchWorkOrders(workOrdersData, customersData, techniciansData) {
  console.log(`[Worker] Processing ${workOrdersData.length} ZenWrench work orders`);

  const customerMap = new Map(customersData.map(c => [c._id, c]));
  const technicianMap = new Map(techniciansData.map(t => [t._id, t]));

  return workOrdersData.map(workOrder => {
    const customer = customerMap.get(workOrder.customerId) || {};
    const technician = technicianMap.get(workOrder.technicianId) || {};

    return {
      ...workOrder,
      customer: {
        ...customer,
        displayName: customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'Unknown Customer'
      },
      technician: {
        ...technician,
        displayName: technician.name || `${technician.firstName || ''} ${technician.lastName || ''}`.trim() || 'Unassigned'
      },
      displayInfo: {
        workOrderNumber: workOrder.number || workOrder._id,
        customerName: customer.name || 'Unknown Customer',
        technicianName: technician.name || 'Unassigned',
        statusDisplay: workOrder.status || 'pending'
      }
    };
  });
}

/**
 * PERFORMANCE FIX: Process generic table data in web worker
 */
async function processTableData(tableData, relatedData = {}, options = {}) {
  console.log(`[Worker] Processing ${tableData.length} table rows`);

  if (!Array.isArray(tableData)) {
    return tableData;
  }

  return tableData.map((row, index) => ({
    ...row,
    processed: true,
    processedAt: new Date().toISOString(),
    rowIndex: index,
    relatedDataKeys: Object.keys(relatedData),
    appName: options.appName || 'unknown'
  }));
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}
