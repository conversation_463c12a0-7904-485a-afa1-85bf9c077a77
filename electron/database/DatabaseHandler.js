/**
 * Ultra-Simplified Database Handler - KISS principle
 * Only essential database operations
 */

const PouchDB = require('pouchdb');
const PouchDBFind = require('pouchdb-find');
const path = require('path');
const { EventEmitter } = require('events');
const fs = require('fs');
const { app } = require('electron');
const AttachmentManager = require('./attachment/AttachmentManager');
const SimpleConflictResolver = require('./conflict/SimpleConflictResolver');

// MEMORY LEAK FIX: Patch PouchDB to increase max listeners on all instances
const originalPouchDB = PouchDB;
function PatchedPouchDB(...args) {
  const instance = new originalPouchDB(...args);

  // Set max listeners on the PouchDB instance
  if (instance.setMaxListeners) {
    instance.setMaxListeners(100);
  }

  // Patch the sync method to set max listeners on sync handlers
  const originalSync = instance.sync;
  instance.sync = function (...syncArgs) {
    const syncHandler = originalSync.apply(this, syncArgs);
    if (syncHandler && syncHandler.setMaxListeners) {
      syncHandler.setMaxListeners(50);
    }
    return syncHandler;
  };

  // Patch the changes method to set max listeners on change handlers
  const originalChanges = instance.changes;
  instance.changes = function (...changeArgs) {
    const changeHandler = originalChanges.apply(this, changeArgs);
    if (changeHandler && changeHandler.setMaxListeners) {
      changeHandler.setMaxListeners(50);
    }
    return changeHandler;
  };

  return instance;
}

// Copy static methods and properties
Object.setPrototypeOf(PatchedPouchDB, originalPouchDB);
Object.assign(PatchedPouchDB, originalPouchDB);

// Use patched PouchDB
const PatchedPouchDBWithFind = PatchedPouchDB.plugin(PouchDBFind);

/**
 * Utility to clean up sync handlers to prevent memory leaks.
 */
function cleanupSyncHandler(handler) {
  try {
    if (handler && typeof handler.removeAllListeners === 'function') {
      handler.removeAllListeners();
    }
    if (handler && typeof handler.cancel === 'function') {
      handler.cancel();
    }
  } catch (cleanupError) {
    // Ignore cleanup errors
  }
}

class DatabaseHandler extends EventEmitter {
  constructor() {
    super();
    this.setMaxListeners(50); // Increased to handle multiple database connections
    this.databases = new Map();
    this.currentUser = {
      label: "System",
      value: "system"
    };
    this.databasePath = path.join(app.getPath('userData'), '.database');

    // PERFORMANCE FIX: Simple connection pooling
    this.connectionPool = new Map(); // Pool of database connections by dbKey
    this.connectionStats = new Map(); // Connection usage statistics
    this.maxConnectionsPerDb = 3; // Maximum connections per database
    this.connectionTimeout = 15000; // 15 seconds
    this.idleTimeout = 300000; // 5 minutes

    // Initialize AttachmentManager
    this.attachmentManager = new AttachmentManager(this);

    // Initialize Simple ConflictResolver for high performance
    this.conflictResolver = new SimpleConflictResolver(this);

    // Simple conflict resolver doesn't need complex event listeners

    // Define essential databases and their prefixes
    this.essentialDatabases = {
      // Mission Control global databases (no organization prefix)
      'mission_control': ['organizations', 'subscriptions', 'plans', 'softwares'],
      // Standard organization databases (with organization prefix)
      'standard': ['users', 'roles', 'branches', 'settings', 'logs']
    };

    if (!fs.existsSync(this.databasePath)) {
      fs.mkdirSync(this.databasePath, { recursive: true });
    }

    // Start connection cleanup
    this.startConnectionCleanup();
  }

  /**
   * PERFORMANCE FIX: Start connection cleanup interval
   */
  startConnectionCleanup() {
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleConnections();
    }, 60000); // Check every minute
  }

  /**
   * PERFORMANCE FIX: Cleanup idle database connections
   */
  cleanupIdleConnections() {
    const now = Date.now();

    for (const [dbKey, pool] of this.connectionPool) {
      const stats = this.connectionStats.get(dbKey);
      if (!stats) continue;

      // Remove connections that have been idle too long
      const connectionsToRemove = [];
      pool.forEach((conn, index) => {
        if (!conn.inUse && (now - conn.lastUsed) > this.idleTimeout) {
          connectionsToRemove.push(index);
        }
      });

      // Remove idle connections (keep at least 1)
      connectionsToRemove.reverse().forEach(index => {
        if (pool.length > 1) {
          const conn = pool[index];
          try {
            if (conn.db && typeof conn.db.close === 'function') {
              conn.db.close();
            }
          } catch (error) {
            console.warn(`[DatabaseHandler] Error closing connection for ${dbKey}:`, error);
          }
          pool.splice(index, 1);
          stats.totalConnections--;
        }
      });

      if (connectionsToRemove.length > 0) {
        console.log(`[DatabaseHandler] Cleaned up ${connectionsToRemove.length} idle connections for ${dbKey}`);
      }
    }
  }

  /**
   * PERFORMANCE FIX: Get or create a pooled database connection
   */
  async getPooledConnection(dbKey) {
    if (!this.connectionPool.has(dbKey)) {
      this.connectionPool.set(dbKey, []);
      this.connectionStats.set(dbKey, {
        totalConnections: 0,
        activeConnections: 0,
        created: 0,
        reused: 0
      });
    }

    const pool = this.connectionPool.get(dbKey);
    const stats = this.connectionStats.get(dbKey);

    // Try to find an idle connection
    const idleConnection = pool.find(conn => !conn.inUse);
    if (idleConnection) {
      idleConnection.inUse = true;
      idleConnection.lastUsed = Date.now();
      stats.activeConnections++;
      stats.reused++;
      return idleConnection.db;
    }

    // Create new connection if under limit
    if (pool.length < this.maxConnectionsPerDb) {
      const dbConfig = this.databases.get(dbKey);
      if (dbConfig) {
        const connection = {
          db: dbConfig.db, // Use existing database instance
          inUse: true,
          created: Date.now(),
          lastUsed: Date.now()
        };

        pool.push(connection);
        stats.totalConnections++;
        stats.activeConnections++;
        stats.created++;

        return connection.db;
      }
    }

    // Fallback to direct database access
    const dbConfig = this.databases.get(dbKey);
    return dbConfig ? dbConfig.db : null;
  }

  /**
   * PERFORMANCE FIX: Release a pooled database connection
   */
  releasePooledConnection(dbKey, db) {
    const pool = this.connectionPool.get(dbKey);
    const stats = this.connectionStats.get(dbKey);

    if (pool && stats) {
      const connection = pool.find(conn => conn.db === db);
      if (connection) {
        connection.inUse = false;
        connection.lastUsed = Date.now();
        stats.activeConnections--;
      }
    }
  }

  /**
   * Initialize all essential databases for the system
   */
  async initializeEssentialDatabases(organizationPrefix, lan_details, branch) {
    console.log(`[DatabaseHandler] Initializing essential databases for organization: ${organizationPrefix || 'global'}`);

    const results = [];

    // Initialize Mission Control global databases (always initialize these)
    console.log(`[DatabaseHandler] Initializing Mission Control databases...`);
    for (const dbName of this.essentialDatabases.mission_control) {
      try {
        const result = await this.initializeDatabase(dbName, 'mission_control_', lan_details, branch);
        results.push({ database: `mission_control_${dbName}`, ...result });
        console.log(`[DatabaseHandler] ✅ Initialized global database: mission_control_${dbName}`);
      } catch (error) {
        console.error(`[DatabaseHandler] ❌ Failed to initialize mission_control_${dbName}:`, error);
        results.push({ database: `mission_control_${dbName}`, success: false, error: error.message });
      }
    }

    // Initialize standard organization databases (if organization prefix provided)
    if (organizationPrefix) {
      for (const dbName of this.essentialDatabases.standard) {
        try {
          const result = await this.initializeDatabase(dbName, organizationPrefix, lan_details, branch);
          results.push({ database: `${organizationPrefix}${dbName}`, ...result });
          console.log(`[DatabaseHandler] ✅ Initialized organization database: ${organizationPrefix}${dbName}`);
        } catch (error) {
          console.error(`[DatabaseHandler] ❌ Failed to initialize ${organizationPrefix}${dbName}:`, error);
          results.push({ database: `${organizationPrefix}${dbName}`, success: false, error: error.message });
        }
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    console.log(`[DatabaseHandler] Essential databases initialized: ${successCount}/${totalCount} successful`);

    return {
      success: successCount === totalCount,
      results: results,
      summary: `${successCount}/${totalCount} databases initialized`
    };
  }

  /**
   * Initialize database with smart primary selection and 3-way sync
   * Automatically handles essential database prefix correction
   */
  async initializeDatabase(name, databasePrefix, lan_details, branch) {
    // Check if this is an essential database and get the correct prefix
    const essentialInfo = this.getEssentialDatabaseInfo(name, databasePrefix);

    let finalPrefix = databasePrefix;
    if (essentialInfo.isEssential) {
      if (essentialInfo.type === 'mission_control') {
        finalPrefix = 'mission_control_';
        console.log(`[DatabaseHandler] Correcting prefix for mission control database ${name}: ${databasePrefix || 'none'} -> mission_control_`);
      } else if (essentialInfo.type === 'organization') {
        // Keep the organization prefix as-is
        finalPrefix = databasePrefix;
        console.log(`[DatabaseHandler] Keeping organization prefix for ${name}: ${databasePrefix}`);
      } else if (essentialInfo.type === 'standard' && !databasePrefix) {
        console.warn(`[DatabaseHandler] Standard essential database ${name} requires organization prefix`);
      }
    }

    const dbKey = `${finalPrefix || ''}${name}`;
    if (this.databases.has(dbKey)) {
      console.log(`[DatabaseHandler] Database ${dbKey} already initialized`);
      return { success: true, dbKey: dbKey, reason: 'Already exists' };
    }

    try {
      // Always create local PouchDB for offline capability
      const localDbPath = path.join(this.databasePath, name);
      const localDb = new PatchedPouchDBWithFind(localDbPath, { adapter: 'leveldb' });

      // MEMORY LEAK FIX: Set max listeners on PouchDB instances
      if (localDb.setMaxListeners) {
        localDb.setMaxListeners(100);
      }

      let primaryDb = localDb;
      let lanDb = null;
      const remoteUrl = `https://therick:<EMAIL>/${dbKey}`;

      // If LAN details provided, try to use LAN CouchDB as primary
      if (lan_details && lan_details.host && lan_details.port) {
        try {
          const lanUrl = `http://${lan_details.username}:${lan_details.password}@${lan_details.host}:${lan_details.port}/${dbKey}`;
          lanDb = new PatchedPouchDBWithFind(lanUrl);

          // MEMORY LEAK FIX: Set max listeners on LAN database
          if (lanDb.setMaxListeners) {
            lanDb.setMaxListeners(100);
          }

          // PERFORMANCE FIX: Test LAN connectivity with reduced timeout for faster fallback
          await Promise.race([
            lanDb.info(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('LAN connection timeout')), 1500))
          ]);

          primaryDb = lanDb; // Use LAN as primary only if connection succeeds
          console.log(`[DatabaseHandler] Using LAN CouchDB as primary for ${dbKey}`);
        } catch (lanError) {
          console.warn(`[DatabaseHandler] LAN CouchDB unavailable for ${dbKey}, using local as primary:`, lanError.message);
          // Ensure we fall back to local as primary
          primaryDb = localDb;
          lanDb = null; // Clear failed LAN database
        }
      }

      // Store database configuration
      this.databases.set(dbKey, {
        db: primaryDb,           // Primary database (LAN or Local)
        localDb: localDb,        // Always available local database
        lanDb: lanDb,           // LAN database if available
        remoteUrl: remoteUrl,   // Remote database URL
        name: name,
        isPrimaryLan: primaryDb === lanDb,
        lan_details: lan_details,
        branch: this.normalizeBranch(branch), // Store normalized branch for filtering
        // SYNC FIX: Track sequences instead of timestamps for proper incremental sync
        syncSequences: {
          localToLan: null,      // Sequence for local ↔ LAN sync
          lanToRemote: null,     // Sequence for LAN ↔ remote sync
          localToRemote: null    // Sequence for local ↔ remote sync (fallback)
        },
        lastSyncTime: Date.now() // Keep timestamp for metrics only
      });

      // Start 3-way sync in background
      this.setup3WaySync(dbKey);

      // Setup change listeners for automatic conflict detection
      this.setupChangeListeners(dbKey);

      // RACE CONDITION FIX: Emit event when database is fully initialized
      this.emit('databaseInitialized', { dbKey, config: this.databases.get(dbKey) });

      console.log(`[DatabaseHandler] Database ${dbKey} initialized with ${primaryDb === lanDb ? 'LAN' : 'Local'} primary`);
      return { success: true, dbKey };
    } catch (error) {
      console.error(`[DatabaseHandler] Failed to initialize ${dbKey}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Setup efficient 3-way sync (Local ↔ LAN ↔ Remote)
   * PERFORMANCE FIX: Skip sync setup if no LAN database is available
   */
  setup3WaySync(dbKey) {
    const config = this.databases.get(dbKey);
    if (!config) return;

    // PERFORMANCE FIX: Skip sync setup if no LAN database is available
    // This prevents unnecessary sync attempts that would timeout
    if (!config.lanDb && !config.isPrimaryLan) {
      console.log(`[DatabaseHandler] Skipping 3-way sync setup for ${dbKey} - no LAN database available`);
      return;
    }

    // Sync every 30 seconds (efficient interval)
    const syncInterval = setInterval(async () => {
      try {
        await this.perform3WaySync(dbKey);
      } catch (error) {
        console.error(`[DatabaseHandler] 3-way sync error for ${dbKey}:`, error.message);
      }
    }, 30000);

    // Store interval for cleanup
    config.syncInterval = syncInterval;
  }

  /**
   * Setup periodic remote server availability check
   */
  setupRemoteServerMonitoring() {
    // Check remote server availability every 2 minutes
    const availabilityInterval = setInterval(async () => {
      try {
        await this.updateRemoteSyncStatus();
      } catch (error) {
        console.error('[DatabaseHandler] Remote server availability check failed:', error.message);
      }
    }, 120000); // 2 minutes

    // Store interval for cleanup
    this.remoteServerMonitoringInterval = availabilityInterval;

    // Initial check
    this.updateRemoteSyncStatus().catch(error => {
      console.error('[DatabaseHandler] Initial remote server availability check failed:', error.message);
    });
  }



  /**
   * Perform efficient 3-way sync with proper sequence tracking
   * PERFORMANCE FIX: Skip sync operations when no LAN database is available
   */
  async perform3WaySync(dbKey) {
    const config = this.databases.get(dbKey);
    if (!config) return;

    const { localDb, lanDb, remoteUrl, syncSequences } = config;
    const syncHandlers = [];
    const startTime = Date.now();

    // PERFORMANCE FIX: Skip sync if no LAN database is available
    // This prevents timeout delays when LAN CouchDB is not accessible
    if (!lanDb && !config.isPrimaryLan) {
      console.log(`[DatabaseHandler] Skipping 3-way sync for ${dbKey} - no LAN database available`);
      return;
    }

    try {
      // SYNC FIX: Use sequences for incremental sync instead of timestamps
      // Step 1: Local ↔ LAN sync (if LAN available)
      if (lanDb) {
        const localToLanOptions = {
          timeout: 3000, // PERFORMANCE FIX: Reduced timeout from 10000ms to 3000ms
          batch_size: 50,
          retry: false,
          include_docs: true
        };

        // SYNC FIX: Only use sequence if it exists and is valid (from previous sync result)
        if (syncSequences.localToLan !== null && syncSequences.localToLan !== undefined) {
          localToLanOptions.since = syncSequences.localToLan;
          console.log(`[DatabaseHandler] Using sequence for ${dbKey} local↔LAN sync: ${syncSequences.localToLan}`);
        } else {
          console.log(`[DatabaseHandler] No sequence available for ${dbKey} local↔LAN sync - performing full sync`);
        }

        const syncHandler1 = localDb.sync(lanDb, localToLanOptions);
        if (syncHandler1.setMaxListeners) syncHandler1.setMaxListeners(50);
        syncHandlers.push(syncHandler1);

        const syncResult1 = await syncHandler1;

        // SYNC FIX: Store the sequence from sync result for next incremental sync
        if (syncResult1 && syncResult1.last_seq) {
          syncSequences.localToLan = syncResult1.last_seq;
          console.log(`[DatabaseHandler] Stored sequence for ${dbKey} local↔LAN: ${syncResult1.last_seq}`);
        }
      }

      // Step 2: Primary ↔ Remote sync (only if remote sync is enabled)
      const primaryDb = config.isPrimaryLan ? lanDb : localDb;
      if (primaryDb && config.remoteSyncEnabled !== false) {
        console.log(`[DatabaseHandler] Performing remote sync for ${dbKey}`);

        const remoteOptions = {
          timeout: 8000, // PERFORMANCE FIX: Reduced timeout from 15000ms to 8000ms
          batch_size: 25,
          retry: false,
          include_docs: true
        };

        // SYNC FIX: Only use sequence if it exists (from previous sync result)
        const sequenceKey = config.isPrimaryLan ? 'lanToRemote' : 'localToRemote';
        if (syncSequences[sequenceKey] !== null && syncSequences[sequenceKey] !== undefined) {
          remoteOptions.since = syncSequences[sequenceKey];
          console.log(`[DatabaseHandler] Using sequence for ${dbKey} ${sequenceKey} sync: ${syncSequences[sequenceKey]}`);
        } else {
          console.log(`[DatabaseHandler] No sequence available for ${dbKey} ${sequenceKey} sync - performing full sync`);
        }

        const syncHandler2 = primaryDb.sync(remoteUrl, remoteOptions);
        if (syncHandler2.setMaxListeners) syncHandler2.setMaxListeners(50);
        syncHandlers.push(syncHandler2);

        const syncResult2 = await syncHandler2;

        // SYNC FIX: Store the sequence from remote sync result
        if (syncResult2 && syncResult2.last_seq) {
          syncSequences[sequenceKey] = syncResult2.last_seq;
          console.log(`[DatabaseHandler] Stored sequence for ${dbKey} ${sequenceKey}: ${syncResult2.last_seq}`);
        }
      } else if (config.remoteSyncEnabled === false) {
        console.log(`[DatabaseHandler] Remote sync skipped for ${dbKey} - disabled`);
      }

      // Step 3: If LAN is primary, ensure local is synced (final step)
      if (config.isPrimaryLan && lanDb && localDb) {
        const finalSyncOptions = {
          timeout: 2000, // PERFORMANCE FIX: Reduced timeout from 5000ms to 2000ms
          batch_size: 50,
          retry: false,
          include_docs: true
        };

        // SYNC FIX: Only use sequence if it exists (from previous sync result)
        if (syncSequences.localToLan !== null && syncSequences.localToLan !== undefined) {
          finalSyncOptions.since = syncSequences.localToLan;
        }

        const syncHandler3 = localDb.sync(lanDb, finalSyncOptions);
        if (syncHandler3.setMaxListeners) syncHandler3.setMaxListeners(50);
        syncHandlers.push(syncHandler3);

        const syncResult3 = await syncHandler3;

        // SYNC FIX: Update sequence from final sync
        if (syncResult3 && syncResult3.last_seq) {
          syncSequences.localToLan = syncResult3.last_seq;
        }
      }

      // SYNC FIX: Update timestamp for metrics only (not used for sync)
      config.lastSyncTime = Date.now();

      // Track performance metrics
      const endTime = Date.now();
      this.trackSyncPerformance(dbKey, startTime, endTime, '3way_sync');

      // Emit real-time sync event
      this.emit('syncComplete', { dbKey });
      this.emit('documentChanged', {
        type: 'sync_complete',
        dbKey: dbKey
      });

    } catch (error) {
      // Track performance metrics even for failed syncs
      const endTime = Date.now();
      this.trackSyncPerformance(dbKey, startTime, endTime, '3way_sync_failed');

      // SYNC FIX: Log sync errors (malformed sequence errors should no longer occur)
      console.warn(`[DatabaseHandler] Sync warning for ${dbKey}:`, error.message);
    } finally {
      // Centralized cleanup
      syncHandlers.forEach(cleanupSyncHandler);
    }
  }

  /**
   * Perform parallel sync for multiple databases (performance optimization)
   */
  async performParallelSync(dbKeys) {
    const syncPromises = dbKeys.map(async (dbKey) => {
      try {
        await this.perform3WaySync(dbKey);
        return { dbKey, success: true };
      } catch (error) {
        console.warn(`[DatabaseHandler] Parallel sync failed for ${dbKey}:`, error.message);
        return { dbKey, success: false, error: error.message };
      }
    });

    const results = await Promise.allSettled(syncPromises);
    return results.map(result => result.value || result.reason);
  }

  /**
   * Smart sync that groups databases by priority and syncs in parallel
   */
  async performSmartSync() {
    const allDbKeys = Array.from(this.databases.keys());

    // Group databases by priority (mission_control first, then others)
    const missionControlDbs = allDbKeys.filter(key => key.includes('mission_control'));
    const otherDbs = allDbKeys.filter(key => !key.includes('mission_control'));

    const results = [];

    // Sync mission control databases first (higher priority)
    if (missionControlDbs.length > 0) {
      console.log(`[DatabaseHandler] Syncing ${missionControlDbs.length} mission control databases`);
      const missionResults = await this.performParallelSync(missionControlDbs);
      results.push(...missionResults);
    }

    // Then sync other databases in parallel
    if (otherDbs.length > 0) {
      console.log(`[DatabaseHandler] Syncing ${otherDbs.length} other databases`);
      const otherResults = await this.performParallelSync(otherDbs);
      results.push(...otherResults);
    }

    return results;
  }

  /**
   * Get the active database (primary determined at initialization)
   */
  async getActiveDatabase(dbKey) {
    let dbConfig = this.databases.get(dbKey);

    // Auto-initialize databases if not found
    if (!dbConfig) {
      const databaseName = this.extractDatabaseName(dbKey);
      const databasePrefix = this.extractDatabasePrefix(dbKey);

      console.log(`[DatabaseHandler] Database ${dbKey} not initialized, attempting auto-initialization...`);

      // Try essential database first
      let initResult = await this.ensureEssentialDatabase(databaseName, databasePrefix, null, 'none');

      // If not essential, initialize as regular database
      if (!initResult.success && initResult.reason === 'Not an essential database') {
        console.log(`[DatabaseHandler] ${dbKey} is not essential, initializing as regular database...`);
        initResult = await this.initializeDatabase(databaseName, databasePrefix, null, 'none');
        initResult.reason = initResult.success ? 'Initialized as regular database' : initResult.error;
      }

      if (initResult.success) {
        dbConfig = this.databases.get(dbKey);
        console.log(`[DatabaseHandler] Successfully auto-initialized ${dbKey}: ${initResult.reason}`);
      } else {
        console.error(`[DatabaseHandler] Auto-initialization failed for ${dbKey}:`, initResult.reason);
        throw new Error(`Database ${dbKey} not initialized and auto-initialization failed: ${initResult.reason}`);
      }
    }

    // Validate and repair database configuration if needed
    const repairResult = await this.validateAndRepairDatabaseConfig(dbKey, dbConfig);
    if (!repairResult.success) {
      throw new Error(`Database ${dbKey} configuration is invalid and could not be repaired: ${repairResult.error}`);
    }

    // Get the potentially repaired config
    dbConfig = this.databases.get(dbKey);

    // PERFORMANCE FIX: Use connection pooling for better performance
    try {
      const pooledConnection = await this.getPooledConnection(dbKey);
      if (pooledConnection) {
        return pooledConnection;
      }
    } catch (error) {
      console.warn(`[DatabaseHandler] Connection pooling failed for ${dbKey}, falling back to direct access:`, error);
    }

    // Fallback: Simply return the primary database - already determined at initialization
    if (!dbConfig.db) {
      throw new Error(`Database ${dbKey} has no available database instances`);
    }

    return dbConfig.db;
  }

  /**
   * Generate document ID
   */
  generateDocumentId() {
    return Date.now().toString(36).toUpperCase();
  }

  /**
   * Generate reference number
   */
  generateReferenceNumber(prefix = '', count = null) {
    const sequence = count !== null
      ? count.toString().padStart(6, '0')
      : Math.floor(Math.random() * 999999).toString().padStart(6, '0');
    const timestamp = Date.now().toString(36).slice(-4).toUpperCase();
    return `${prefix}${timestamp}-${sequence}`;
  }

  /**
   * Enhanced sync method with 3-way sync support
   */
  async syncThis(dbKey, targetUrl, options = {}) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      // If no specific target, perform full 3-way sync
      if (!targetUrl) {
        await this.perform3WaySync(dbKey);
        return { success: true, message: '3-way sync completed' };
      }

      // Sync primary database with specific target
      const syncHandler = dbConfig.db.sync(targetUrl, {
        timeout: 15000,
        batch_size: 5,
        retry: false,
        ...options
      });

      // MEMORY LEAK FIX: Set max listeners on sync handler
      if (syncHandler.setMaxListeners) {
        syncHandler.setMaxListeners(50);
      }

      const syncResult = await syncHandler;

      // MEMORY LEAK FIX: Clean up sync handler
      try {
        if (syncHandler.removeAllListeners) {
          syncHandler.removeAllListeners();
        }
        if (syncHandler.cancel) {
          syncHandler.cancel();
        }
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      return { success: true, ...syncResult };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Create document (with intelligent fallback)
   */
  async create(dbKey, data, user = null) {
    // CRUD OPERATION SYNC CONTROL: Pause sync during create operation
    if (this.syncService) {
      this.syncService.startCrudOperation(dbKey, 'create');
    }

    try {
      const activeDb = await this.getActiveDatabase(dbKey);

      const currentUser = user ? { key: user.value, label: user.label, value: user.value } : { key: this.currentUser.value, label: this.currentUser.label, value: this.currentUser.value };

      const doc = {
        ...data,
        _id: data._id || this.generateDocumentId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: { label: currentUser.label, value: currentUser.value },
        allUpdaters: [currentUser] // Initialize with creator as first updater
      };

      // Write to active database (with fallback)
      const result = await activeDb.put(doc);

      // Add user activity log (but not for logs database to prevent infinite loop)
      if (!this.extractDatabaseName(dbKey).endsWith('logs')) {
        const logData = {
          description: `Created data in ${this.extractDatabaseName(dbKey)}`,
          details: { _id: doc._id, operation: 'create' },
          user: user || this.currentUser,
          action: "create",
          type: "create",
          sourceDatabase: this.extractDatabaseName(dbKey)
        };

        console.log(`[DatabaseHandler] Creating log with details:`, JSON.stringify(logData.details));

        this.addLog(this.extractDatabasePrefix(dbKey), logData).catch(logError => {
          console.warn(`[DatabaseHandler] Failed to log create operation:`, logError);
        });
      }

      // CRUD OPERATION SYNC CONTROL: End CRUD operation and resume sync
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'create');
      } else {
        // PERFORMANCE FIX: Only trigger sync if LAN database is available
        const config = this.databases.get(dbKey);
        if (config && (config.lanDb || config.isPrimaryLan)) {
          // Fallback: Trigger immediate sync for real-time updates if no sync service
          setImmediate(() => this.perform3WaySync(dbKey));
        }
      }

      return { success: true, id: result.id, rev: result.rev };
    } catch (error) {
      console.error(`[DatabaseHandler] Create failed for ${dbKey}:`, error.message);

      // CRUD OPERATION SYNC CONTROL: End CRUD operation even on error
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'create_error');
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Update document (with intelligent fallback)
   */
  async update(dbKey, id, data, user = null) {
    // CRUD OPERATION SYNC CONTROL: Pause sync during update operation
    if (this.syncService) {
      this.syncService.startCrudOperation(dbKey, 'update');
    }

    try {
      const activeDb = await this.getActiveDatabase(dbKey);

      // Get existing document from the same database we'll write to
      const existingDoc = await activeDb.get(id);
      const currentUser = user ? { key: user.value, label: user.label, value: user.value } : { key: this.currentUser.value, label: this.currentUser.label, value: this.currentUser.value };

      // Get existing allUpdaters array or initialize it
      const existingUpdaters = existingDoc.allUpdaters || [];

      // Check if current user is already in allUpdaters
      const userExists = existingUpdaters.some(updater => updater.value === currentUser.value);

      // Add current user to allUpdaters if not already present
      const allUpdaters = userExists ? existingUpdaters : [...existingUpdaters, currentUser];

      const updatedDoc = {
        ...existingDoc,
        ...data,
        _id: id,
        _rev: existingDoc._rev,
        updatedAt: new Date().toISOString(),
        updatedBy: { label: currentUser.label, value: currentUser.value },
        allUpdaters: allUpdaters
      };

      // Write to active database (with fallback)
      const result = await activeDb.put(updatedDoc);

      // Add user activity log (but not for logs database to prevent infinite loop)
      if (!this.extractDatabaseName(dbKey).endsWith('logs')) {
        this.addLog(this.extractDatabasePrefix(dbKey), {
          description: `Updated data in ${this.extractDatabaseName(dbKey)}`,
          details: { _id: id, operation: 'update' },
          user: user || this.currentUser,
          action: "edit",
          type: "edit",
          sourceDatabase: this.extractDatabaseName(dbKey)
        }).catch(logError => {
          console.warn(`[DatabaseHandler] Failed to log update operation:`, logError);
        });
      }

      // CRUD OPERATION SYNC CONTROL: End CRUD operation and resume sync
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'update');
      } else {
        // PERFORMANCE FIX: Only trigger sync if LAN database is available
        const config = this.databases.get(dbKey);
        if (config && (config.lanDb || config.isPrimaryLan)) {
          // Fallback: Trigger immediate sync for real-time updates if no sync service
          setImmediate(() => this.perform3WaySync(dbKey));
        }
      }

      return { success: true, id: result.id, rev: result.rev };
    } catch (error) {
      console.error(`[DatabaseHandler] Update failed for ${dbKey}:`, error.message);

      // CRUD OPERATION SYNC CONTROL: End CRUD operation even on error
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'update_error');
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Get document (with intelligent fallback)
   */
  async get(dbKey, id, options = {}) {
    try {
      const activeDb = await this.getActiveDatabase(dbKey);
      const doc = await activeDb.get(id, options);
      return doc;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete document (with intelligent fallback)
   */
  async delete(dbKey, id) {
    // CRUD OPERATION SYNC CONTROL: Pause sync during delete operation
    if (this.syncService) {
      this.syncService.startCrudOperation(dbKey, 'delete');
    }

    try {
      const activeDb = await this.getActiveDatabase(dbKey);

      // Get document from the same database we'll delete from
      const doc = await activeDb.get(id);

      // Delete from active database (with fallback)
      const result = await activeDb.remove(doc);

      // Add user activity log (but not for logs database to prevent infinite loop)
      if (!this.extractDatabaseName(dbKey).endsWith('logs')) {
        this.addLog(this.extractDatabasePrefix(dbKey), {
          description: `Deleted data from ${this.extractDatabaseName(dbKey)}`,
          details: { _id: id, operation: 'delete' },
          user: this.currentUser,
          action: "delete",
          type: "delete",
          sourceDatabase: this.extractDatabaseName(dbKey)
        }).catch(logError => {
          console.warn(`[DatabaseHandler] Failed to log delete operation:`, logError);
        });
      }

      // CRUD OPERATION SYNC CONTROL: End CRUD operation and resume sync
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'delete');
      } else {
        // PERFORMANCE FIX: Only trigger sync if LAN database is available
        const config = this.databases.get(dbKey);
        if (config && (config.lanDb || config.isPrimaryLan)) {
          // Fallback: Trigger immediate sync for real-time updates if no sync service
          setImmediate(() => this.perform3WaySync(dbKey));
        }
      }

      // Emit delete event for real-time updates
      this.emitDocumentChanged({
        type: 'delete',
        id: id,
        doc: doc,  // Include the deleted document
        dbKey: dbKey
      });

      return { success: true, id: result.id, rev: result.rev };
    } catch (error) {
      console.error(`[DatabaseHandler] Delete failed for ${dbKey}:`, error.message);

      // CRUD OPERATION SYNC CONTROL: End CRUD operation even on error
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'delete_error');
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Find documents (with intelligent fallback and branch filtering)
   */
  async find(dbKey, selector, options = {}) {
    try {
      const activeDb = await this.getActiveDatabase(dbKey);
      const result = await activeDb.find({ selector, ...options });

      let documents = result.docs;

      // Apply branch filtering if branch filter is provided in options
      if (options.branchFilter !== undefined) {
        const isMultiBranch = options.isMultiBranch || false;
        documents = this.applyBranchFilter(documents, options.branchFilter, isMultiBranch);
        console.log(`[DatabaseHandler] Applied branch filter '${options.branchFilter}' to find query in ${dbKey}, returned ${documents.length} documents`);
      }

      return documents;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all documents (with intelligent fallback)
   */
  async allDocs(dbKey, options = {}) {
    try {
      const activeDb = await this.getActiveDatabase(dbKey);
      const result = await activeDb.allDocs(options);
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Enhanced conflict handling with full resolution capabilities
   * DISABLED: Conflict resolution disabled for performance
   */
  async handleConflicts(change) {
    if (change?.doc?._conflicts?.length) {
      console.log(`[DatabaseHandler] Conflict detected for ${change.doc._id}, but conflict resolution is disabled for performance`);
      // Conflict resolution disabled - no action taken
    }
  }

  /**
   * Get all databases
   */
  getAllDatabases() {
    return Array.from(this.databases.keys());
  }

  /**
   * Get sync status with sequence information
   */
  getSyncStatus() {
    const syncStatus = {
      isHealthy: true,
      lastSyncTimes: {},
      pendingOperations: new Set(),
      syncSequences: {} // SYNC FIX: Include sequence information
    };

    // Collect sync sequences from all databases
    for (const [dbKey, config] of this.databases.entries()) {
      if (config.lastSyncTime) {
        syncStatus.lastSyncTimes[dbKey] = config.lastSyncTime;
      }
      if (config.syncSequences) {
        syncStatus.syncSequences[dbKey] = { ...config.syncSequences };
      }
    }

    return syncStatus;
  }

  /**
   * Method aliases for backward compatibility with IPC handlers
   */
  async getDocument(dbKey, id, options = {}) {
    return this.get(dbKey, id, options);
  }

  async getAllDocuments(dbKey, options = {}) {
    try {
      console.log(`[DatabaseHandler] Getting all documents for ${dbKey}...`);
      const activeDb = await this.getActiveDatabase(dbKey);

      if (!activeDb) {
        throw new Error(`No active database returned for ${dbKey}`);
      }

      console.log(`[DatabaseHandler] Active database obtained for ${dbKey}, calling allDocs...`);

      // PERFORMANCE FIX: Add timeout to prevent IPC hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Database query timeout for ${dbKey}`)), 30000); // 30 second timeout
      });

      const queryPromise = activeDb.allDocs({
        include_docs: true,
        ...options
      });

      const result = await Promise.race([queryPromise, timeoutPromise]);

      // Filter out design documents and get just the documents
      let documents = result.rows
        .filter(row => row.doc && !row.doc._id.startsWith('_'))
        .map(row => row.doc);

      console.log(`[DatabaseHandler] Retrieved ${documents.length} documents from ${dbKey}`);

      // Apply branch filtering if branch filter is provided in options
      if (options.branchFilter !== undefined) {
        const isMultiBranch = options.isMultiBranch || false;
        documents = this.applyBranchFilter(documents, options.branchFilter, isMultiBranch);
        console.log(`[DatabaseHandler] Applied branch filter '${options.branchFilter}' to ${dbKey}, returned ${documents.length} documents`);
      }

      // PERFORMANCE FIX: Release pooled connection
      this.releasePooledConnection(dbKey, activeDb);

      return documents;
    } catch (error) {
      console.error(`[DatabaseHandler] getAllDocuments failed for ${dbKey}:`, error.message);
      console.error(`[DatabaseHandler] Error stack:`, error.stack);

      // PERFORMANCE FIX: Release pooled connection on error
      try {
        const activeDb = await this.getActiveDatabase(dbKey);
        if (activeDb) {
          this.releasePooledConnection(dbKey, activeDb);
        }
      } catch (releaseError) {
        console.warn(`[DatabaseHandler] Failed to release connection for ${dbKey}:`, releaseError.message);
      }

      // Check if database exists in our registry
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        console.error(`[DatabaseHandler] Database ${dbKey} not found in registry`);
      } else {
        console.error(`[DatabaseHandler] Database config for ${dbKey}:`, {
          hasDb: !!dbConfig.db,
          hasLocalDb: !!dbConfig.localDb,
          hasLanDb: !!dbConfig.lanDb,
          isPrimaryLan: dbConfig.isPrimaryLan
        });
      }

      // Return empty array instead of throwing to prevent IPC hanging
      return [];
    }
  }

  /**
   * PERFORMANCE FIX: Get document count without loading all documents
   */
  async getDocumentCount(dbKey, options = {}) {
    try {
      console.log(`[DatabaseHandler] Getting document count for ${dbKey}...`);
      const activeDb = await this.getActiveDatabase(dbKey);

      if (!activeDb) {
        throw new Error(`No active database returned for ${dbKey}`);
      }

      // Get count without loading document content
      const result = await activeDb.allDocs({
        include_docs: false, // Don't load document content for counting
        attachments: false,
        conflicts: false,
        ...options
      });

      // Filter out design documents
      let count = result.rows.filter(row => !row.id.startsWith('_')).length;

      // Apply branch filtering if branch filter is provided in options
      if (options.branchFilter !== undefined) {
        // For count with branch filtering, we need to load docs to check branch
        const docsResult = await activeDb.allDocs({
          include_docs: true,
          attachments: false,
          conflicts: false,
          ...options
        });

        let documents = docsResult.rows
          .filter(row => row.doc && !row.doc._id.startsWith('_'))
          .map(row => row.doc);

        const isMultiBranch = options.isMultiBranch || false;
        documents = this.applyBranchFilter(documents, options.branchFilter, isMultiBranch);
        count = documents.length;
        console.log(`[DatabaseHandler] Applied branch filter '${options.branchFilter}' to count for ${dbKey}: ${count} documents`);
      }

      console.log(`[DatabaseHandler] Returning count ${count} for ${dbKey}`);
      return count;
    } catch (error) {
      console.error(`[DatabaseHandler] getDocumentCount failed for ${dbKey}:`, error);
      return 0;
    }
  }

  async saveDocument(dbKey, data, user = null) {
    // CRUD OPERATION SYNC CONTROL: Pause sync during save operation
    const isUpdate = !!data._id;
    if (this.syncService) {
      this.syncService.startCrudOperation(dbKey, isUpdate ? 'update' : 'create');
    }

    try {
      const activeDb = await this.getActiveDatabase(dbKey);

      const isUpdate = !!data._id;
      const docId = data._id || this.generateDocumentId();

      // Prepare document with required fields
      const preparedDoc = {
        ...data,
        _id: docId,
        updatedAt: new Date().toISOString(),
        updatedBy: user ? { label: user.label, value: user.value } : { label: this.currentUser.label, value: this.currentUser.value },
        ...(isUpdate ? {} : {
          createdAt: new Date().toISOString(),
          createdBy: user ? { label: user.label, value: user.value } : { label: this.currentUser.label, value: this.currentUser.value }
        })
      };

      // Get existing document for updates to preserve _rev and handle allUpdaters
      if (isUpdate) {
        try {
          const existingDoc = await activeDb.get(docId);
          preparedDoc._rev = existingDoc._rev;

          // Handle allUpdaters for updates
          const currentUser = user ? { key: user.value, label: user.label, value: user.value } : { key: this.currentUser.value, label: this.currentUser.label, value: this.currentUser.value };
          const existingUpdaters = existingDoc.allUpdaters || [];
          const userExists = existingUpdaters.some(updater => updater.value === currentUser.value);
          preparedDoc.allUpdaters = userExists ? existingUpdaters : [...existingUpdaters, currentUser];
        } catch (err) {
          if (err.status !== 404) {
            throw err;
          }
          // Document doesn't exist, treat as new
        }
      }

      // Save to active database (with fallback)
      const result = await activeDb.put(preparedDoc);
      preparedDoc._rev = result.rev;

      console.log(`[DatabaseHandler] SaveDocument succeeded for ${dbKey}: ${docId}`);

      // Add user activity log (but not for logs database to prevent infinite loop)
      if (!this.extractDatabaseName(dbKey).endsWith('logs')) {
        this.addLog(this.extractDatabasePrefix(dbKey), {
          description: `${isUpdate ? 'Updated' : 'Created'} data in ${this.extractDatabaseName(dbKey)}`,
          details: { _id: docId, operation: isUpdate ? 'update' : 'create' },
          user: user || this.currentUser,
          action: isUpdate ? "edit" : "create",
          type: isUpdate ? "edit" : "create",
          sourceDatabase: this.extractDatabaseName(dbKey)
        }).catch(logError => {
          console.warn(`[DatabaseHandler] Failed to log save operation:`, logError);
        });
      }

      // CRUD OPERATION SYNC CONTROL: End CRUD operation and resume sync
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, isUpdate ? 'update' : 'create');
      } else {
        // PERFORMANCE FIX: Only trigger sync if LAN database is available
        const config = this.databases.get(dbKey);
        if (config && (config.lanDb || config.isPrimaryLan)) {
          // Fallback: Trigger immediate sync for real-time updates if no sync service
          setImmediate(() => this.perform3WaySync(dbKey));
        }
      }

      // Emit event for compatibility with dbKey for proper routing
      this.emitDocumentChanged({
        type: isUpdate ? 'update' : 'create',
        id: docId,
        doc: preparedDoc,
        dbKey: dbKey  // CRITICAL: Include dbKey for event routing
      });

      // Return format matching CapacitorDB - always return id and doc
      return { id: docId, doc: preparedDoc };
    } catch (error) {
      console.error(`[DatabaseHandler] SaveDocument failed for ${dbKey}:`, error.message);

      // CRUD OPERATION SYNC CONTROL: End CRUD operation even on error
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, isUpdate ? 'update_error' : 'create_error');
      }

      // Return error format but still include id if we have it
      const docId = data._id || this.generateDocumentId();
      return {
        success: false,
        error: error.message,
        id: docId,
        doc: null
      };
    }
  }

  async deleteDocument(dbKey, id, user = null) {
    return this.delete(dbKey, id, user);
  }

  async findDocuments(dbKey, selector, options = {}) {
    return this.find(dbKey, selector, options);
  }

  /**
   * PERFORMANCE FIX: Optimized bulk save using native PouchDB bulkDocs
   */
  async bulkSave(dbKey, docs, user = null) {
    try {
      console.log(`[DatabaseHandler] Bulk saving ${docs.length} documents to ${dbKey}`);

      if (!docs || docs.length === 0) {
        return [];
      }

      // CRUD OPERATION SYNC CONTROL: Start CRUD operation
      if (this.syncService) {
        this.syncService.startCrudOperation(dbKey, 'bulk_create');
      }

      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }

      const activeDb = await this.getActiveDatabase(dbKey);
      const timestamp = new Date().toISOString();
      const baseTimestamp = Date.now();

      // Prepare documents for bulk operation
      const preparedDocs = docs.map((doc, index) => {
        const isUpdate = !!doc._id;
        const docId = doc._id || (baseTimestamp + index).toString(36).toUpperCase();

        return {
          ...doc,
          _id: docId,
          updatedAt: timestamp,
          ...(isUpdate ? {} : {
            createdAt: timestamp,
            createdBy: user?.name || this.currentUser.name,
            referenceNumber: doc.referenceNumber || this.generateReferenceNumber('', index + 1)
          })
        };
      });

      // Use native PouchDB bulkDocs for optimal performance
      const bulkResult = await activeDb.bulkDocs(preparedDocs);

      // Process results and handle errors
      const results = bulkResult.map((result, index) => {
        if (result.ok) {
          return {
            success: true,
            id: result.id,
            rev: result.rev,
            doc: preparedDocs[index]
          };
        } else {
          return {
            success: false,
            id: result.id,
            error: result.error || result.reason || 'Unknown error'
          };
        }
      });

      // Emit bulk change event for successful documents
      const successfulDocs = results
        .filter(r => r.success)
        .map(r => r.doc);

      if (successfulDocs.length > 0) {
        this.emit('documentChanged', {
          type: 'bulk_create',
          dbKey: dbKey,
          docs: successfulDocs
        });
      }

      // CRUD OPERATION SYNC CONTROL: End CRUD operation
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'bulk_create');
      }

      console.log(`[DatabaseHandler] Bulk save completed: ${results.filter(r => r.success).length}/${docs.length} successful`);
      return results;

    } catch (error) {
      console.error(`[DatabaseHandler] Bulk save failed for ${dbKey}:`, error);

      // CRUD OPERATION SYNC CONTROL: End CRUD operation even on error
      if (this.syncService) {
        this.syncService.endCrudOperation(dbKey, 'bulk_create_error');
      }

      // Return error results for all documents
      return docs.map((doc, index) => ({
        success: false,
        id: doc._id || `error_${index}`,
        error: error.message
      }));
    }
  }

  // Stub methods for compatibility - return simple responses
  async closeDatabase(dbKey) {
    return { success: true };
  }

  async closeAllDatabases() {
    return { success: true };
  }

  async createIndex(dbKey, index) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }
      await dbConfig.db.createIndex(index);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async getSyncStats(dbKey) {
    return { success: true, stats: {} };
  }

  async forceSync(dbKey) {
    return { success: true };
  }

  /**
   * Add a log entry for user activity tracking
   * Implements proper logging functionality matching CapacitorDB
   */
  async addLog(databasePrefix, logData) {
    try {
      const logsDbKey = `${databasePrefix || ''}logs`;

      // Initialize logs database if not exists
      if (!this.databases.has(logsDbKey)) {
        await this.initializeDatabase('logs', databasePrefix, null, 'none');
      }

      // Prepare log entry with required fields
      const logEntry = {
        ...logData,
        _id: Date.now().toString(36).toUpperCase(),
        createdAt: new Date().toISOString(),
        sourceDatabase: logData.sourceDatabase || 'unknown',
        databasePrefix: databasePrefix || '',
        branch: logData.branch || 'none',
        deviceInfo: {
          platform: 'electron',
          timestamp: new Date().toISOString(),
          userAgent: 'Electron App'
        }
      };

      // Debug: Check if details are being preserved
      console.log(`[DatabaseHandler] Adding log with details:`, JSON.stringify(logEntry.details));
      console.log(`[DatabaseHandler] Full log entry:`, JSON.stringify(logEntry, null, 2));

      // Save log entry directly to logs database (like CapacitorDB)
      const dbConfig = this.databases.get(logsDbKey);
      if (!dbConfig) {
        throw new Error(`Logs database ${logsDbKey} not initialized`);
      }

      // Use direct database access to avoid double-processing
      const activeDb = await this.getActiveDatabase(logsDbKey);
      await activeDb.put(logEntry);

      console.log(`[DatabaseHandler] Log added successfully: ${logEntry.action} - ${logEntry.description}`);
      return { success: true, id: logEntry._id };
    } catch (error) {
      console.warn(`[DatabaseHandler] Silent error in addLog:`, error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Append a conflict resolution audit entry to a log file.
   */
  logConflictResolutionAudit(auditEntry) {
    const logPath = path.join(this.databasePath, 'conflict-audit.log');
    const logLine = JSON.stringify(auditEntry) + '\n';
    fs.appendFile(logPath, logLine, err => {
      if (err) {
        console.warn('[DatabaseHandler] Failed to write conflict audit log:', err.message);
      }
    });
  }

  // Attachment methods - proper implementation using AttachmentManager
  async getAttachment(dbKey, id, name) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        console.error(`[DatabaseHandler] Database ${dbKey} not found for getAttachment`);
        return null;
      }

      const activeDb = await this.getActiveDatabase(dbKey);
      return await this.attachmentManager.getAttachment(activeDb, dbConfig, id, name);
    } catch (error) {
      console.error(`[DatabaseHandler] getAttachment failed for ${dbKey}:`, error);
      return null;
    }
  }

  async putAttachment(dbKey, id, name, rev, attachment, type, user = null) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      const activeDb = await this.getActiveDatabase(dbKey);
      return await this.attachmentManager.putAttachment(activeDb, dbConfig, id, name, rev, attachment, type, user);
    } catch (error) {
      console.error(`[DatabaseHandler] putAttachment failed for ${dbKey}:`, error);
      throw error;
    }
  }

  async removeAttachment(dbKey, id, name, rev, user = null) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not found`);
      }

      const activeDb = await this.getActiveDatabase(dbKey);
      return await this.attachmentManager.removeAttachment(activeDb, dbConfig, id, name, rev, user);
    } catch (error) {
      console.error(`[DatabaseHandler] removeAttachment failed for ${dbKey}:`, error);
      throw error;
    }
  }

  async authenticate(dbKey, username, password) {
    return { success: true };
  }

  async updateLanDetails(dbKey, lan_details) {
    return { success: true };
  }

  // Additional methods to match CapacitorDB API
  async save(dbKey, data, user = null) {
    return this.saveDocument(dbKey, data, user);
  }

  async getAllData(dbKey, options = {}) {
    return this.getAllDocuments(dbKey, options);
  }

  async bulkAdd(dbKey, docs, user = null) {
    return this.bulkSave(dbKey, docs, user);
  }

  async addDocument(dbKey, doc, user = null) {
    return this.saveDocument(dbKey, doc, user);
  }

  // Utility methods
  normalizeBranch(branch) {
    if (!branch || branch === 'null' || branch === 'undefined') {
      return 'none';
    }
    if (branch === 'all') {
      return 'all';
    }
    return branch;
  }

  /**
   * Apply branch filtering to documents
   */
  applyBranchFilter(documents, selectedBranch, isMultiBranch = false, branchField = 'branch') {
    // Ensure documents is an array
    if (!documents || !Array.isArray(documents)) {
      return [];
    }

    // If this is a multi-branch module, don't apply branch filtering
    if (isMultiBranch) {
      return documents;
    }

    // If no branch is selected or documents is empty, return as is
    if (!selectedBranch || documents.length === 0) {
      return documents;
    }

    // Apply branch filtering based on selection
    if (selectedBranch === "all") {
      // "all" means show all documents regardless of branch
      return documents;
    } else if (selectedBranch === "none") {
      // "none" means show only documents with no branch or "none" as branch
      return documents.filter(doc =>
        doc && (
          !doc[branchField] ||
          doc[branchField] === "none" ||
          doc[branchField] === ""
        )
      );
    } else {
      // Specific branch selected - show only documents with that branch
      return documents.filter(doc => doc && doc[branchField] === selectedBranch);
    }
  }

  /**
   * Get current branch from localStorage (for compatibility with frontend)
   */
  getCurrentBranch() {
    // In Electron main process, we don't have access to localStorage
    // This would need to be passed from the renderer process
    // For now, return 'all' to show all documents
    return 'all';
  }

  /**
   * Extract database prefix from dbKey (e.g., "company_abc_users" -> "company_abc_")
   */
  extractDatabasePrefix(dbKey) {
    const parts = dbKey.split('_');
    if (parts.length > 1) {
      // Return all parts except the last one, with trailing underscore
      return parts.slice(0, -1).join('_') + '_';
    }
    return '';
  }

  /**
   * Extract database name from dbKey (e.g., "company_abc_users" -> "users")
   */
  extractDatabaseName(dbKey) {
    const parts = dbKey.split('_');
    return parts[parts.length - 1];
  }

  /**
   * Check if a database is essential and get its correct prefix
   * @param {string} databaseName - Database name (e.g., "organizations")
   * @param {string} currentPrefix - Current prefix (e.g., "zenwrench_ly5w828y_")
   */
  getEssentialDatabaseInfo(databaseName, currentPrefix = '') {
    // CRITICAL: If the current prefix is NOT 'mission_control_', then this is an organization-specific database
    // Even if the database name is in mission_control list (like 'organizations')

    if (currentPrefix && !currentPrefix.includes('mission_control')) {
      // This is an organization-specific database, not a mission control database
      console.log(`[DatabaseHandler] ${currentPrefix}${databaseName} is an organization-specific database, not mission control`);

      // Check if it's a standard organization database type
      if (this.essentialDatabases.standard.includes(databaseName) ||
        this.essentialDatabases.mission_control.includes(databaseName)) {
        return {
          isEssential: true,
          type: 'organization',
          correctPrefix: currentPrefix, // Keep the organization prefix
          description: 'Organization-specific database'
        };
      }
    }

    // Check if it's a Mission Control database (only if prefix is mission_control or empty)
    if (this.essentialDatabases.mission_control.includes(databaseName) &&
      (!currentPrefix || currentPrefix.includes('mission_control'))) {
      return {
        isEssential: true,
        type: 'mission_control',
        correctPrefix: 'mission_control_',
        description: 'Global Mission Control database'
      };
    }

    // Check if it's a standard organization database
    if (this.essentialDatabases.standard.includes(databaseName)) {
      return {
        isEssential: true,
        type: 'standard',
        correctPrefix: currentPrefix || null, // Use provided prefix or null
        description: 'Standard organization database'
      };
    }

    return {
      isEssential: false,
      type: 'module',
      correctPrefix: currentPrefix || null,
      description: 'Module-specific database'
    };
  }

  /**
   * Initialize essential databases automatically when needed
   */
  async ensureEssentialDatabase(databaseName, organizationPrefix, lan_details, branch) {
    const info = this.getEssentialDatabaseInfo(databaseName, organizationPrefix);

    if (!info.isEssential) {
      return { success: false, reason: 'Not an essential database' };
    }

    let correctPrefix;
    if (info.type === 'mission_control') {
      correctPrefix = 'mission_control_';
    } else if (info.type === 'standard' || info.type === 'organization') {
      correctPrefix = organizationPrefix || '';
    }

    const correctDbKey = `${correctPrefix}${databaseName}`;

    // Check if already initialized
    if (this.databases.has(correctDbKey)) {
      return { success: true, dbKey: correctDbKey, reason: 'Already initialized' };
    }

    // Initialize the database
    console.log(`[DatabaseHandler] Auto-initializing essential database: ${correctDbKey}`);
    const result = await this.initializeDatabase(databaseName, correctPrefix, lan_details, branch);

    return {
      success: result.success,
      dbKey: correctDbKey,
      info: info,
      reason: result.success ? 'Initialized successfully' : result.error
    };
  }

  /**
   * Ensure all mission control databases are initialized
   * This is useful for system startup to prevent "not initialized" errors
   */
  async ensureAllMissionControlDatabases(lan_details = null, branch = 'none') {
    console.log(`[DatabaseHandler] Ensuring all mission control databases are initialized...`);

    const results = [];
    for (const dbName of this.essentialDatabases.mission_control) {
      try {
        const result = await this.ensureEssentialDatabase(dbName, 'mission_control_', lan_details, branch);
        results.push({ database: `mission_control_${dbName}`, ...result });

        if (result.success) {
          console.log(`[DatabaseHandler] ✅ Mission control database ready: mission_control_${dbName}`);
        } else {
          console.warn(`[DatabaseHandler] ⚠️ Mission control database issue: mission_control_${dbName} - ${result.reason}`);
        }
      } catch (error) {
        console.error(`[DatabaseHandler] ❌ Failed to ensure mission_control_${dbName}:`, error);
        results.push({ database: `mission_control_${dbName}`, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`[DatabaseHandler] Mission control databases ready: ${successCount}/${results.length}`);

    return {
      success: successCount === results.length,
      results,
      summary: `${successCount}/${results.length} mission control databases ready`
    };
  }

  /**
   * Ensure an organization exists in mission_control_organizations
   * This prevents sync issues when organization documents are missing
   */
  async ensureOrganizationExists(organizationId, organizationData = {}) {
    try {
      const dbKey = 'mission_control_organizations';

      // Ensure the mission control organizations database exists
      let dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        await this.ensureEssentialDatabase('organizations', 'mission_control_', null, 'none');
        dbConfig = this.databases.get(dbKey);
      }

      if (!dbConfig) {
        throw new Error('Could not initialize mission_control_organizations database');
      }

      const orgId = organizationId.toUpperCase();

      // Check if organization already exists
      try {
        const existingOrg = await dbConfig.db.get(orgId);
        console.log(`[DatabaseHandler] Organization ${orgId} already exists`);
        return { success: true, exists: true, organization: existingOrg };
      } catch (error) {
        if (error.status !== 404) {
          throw error; // Re-throw if it's not a "not found" error
        }
      }

      // Create the organization document
      const orgDoc = {
        _id: orgId,
        name: organizationData.name || `Organization ${orgId}`,
        created: new Date().toISOString(),
        type: 'auto-created',
        ...organizationData
      };

      const result = await dbConfig.db.put(orgDoc);
      console.log(`[DatabaseHandler] ✅ Created organization ${orgId} in mission_control_organizations`);

      // Emit event for real-time updates
      this.emit('documentChanged', {
        type: 'organization_created',
        dbKey: dbKey,
        doc: { ...orgDoc, _rev: result.rev }
      });

      return {
        success: true,
        created: true,
        organization: { ...orgDoc, _rev: result.rev }
      };
    } catch (error) {
      console.error(`[DatabaseHandler] Failed to ensure organization ${organizationId}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate and repair database configuration
   * This fixes issues where database instances become undefined
   */
  async validateAndRepairDatabaseConfig(dbKey, dbConfig) {
    try {
      console.log(`[DatabaseHandler] Validating database config for ${dbKey}...`);

      // Check if dbConfig is null or undefined
      if (!dbConfig) {
        console.error(`[DatabaseHandler] Database config for ${dbKey} is null/undefined, attempting re-initialization...`);

        // Try to re-initialize the database
        const databaseName = this.extractDatabaseName(dbKey);
        const databasePrefix = this.extractDatabasePrefix(dbKey);

        console.log(`[DatabaseHandler] Re-initializing ${dbKey} (name: ${databaseName}, prefix: ${databasePrefix})`);

        // CRITICAL: For organization-specific databases, don't use ensureEssentialDatabase
        // as it might incorrectly convert them to mission control databases
        const initResult = await this.initializeDatabase(databaseName, databasePrefix, null, 'none');

        if (initResult.success) {
          console.log(`[DatabaseHandler] Successfully repaired null database config for ${dbKey}`);
          return { success: true, repaired: true };
        } else {
          console.error(`[DatabaseHandler] Failed to repair null database config for ${dbKey}:`, initResult.error);
          return { success: false, error: `Repair failed: ${initResult.error}` };
        }
      }

      // Check if configuration is completely invalid
      if (!dbConfig.db && !dbConfig.localDb && !dbConfig.lanDb) {
        console.error(`[DatabaseHandler] Database ${dbKey} has no valid database instances, attempting repair...`);

        // Try to re-initialize the database
        const databaseName = this.extractDatabaseName(dbKey);
        const databasePrefix = this.extractDatabasePrefix(dbKey);

        console.log(`[DatabaseHandler] Re-initializing ${dbKey} (name: ${databaseName}, prefix: ${databasePrefix})`);
        const initResult = await this.initializeDatabase(databaseName, databasePrefix, null, 'none');

        if (initResult.success) {
          console.log(`[DatabaseHandler] Successfully repaired database config for ${dbKey}`);
          return { success: true, repaired: true };
        } else {
          console.error(`[DatabaseHandler] Failed to repair database config for ${dbKey}:`, initResult.error);
          return { success: false, error: `Repair failed: ${initResult.error}` };
        }
      }

      // Check if primary database is missing but we have alternatives
      if (!dbConfig.db) {
        if (dbConfig.localDb) {
          console.log(`[DatabaseHandler] Primary database missing for ${dbKey}, using localDb as fallback`);
          dbConfig.db = dbConfig.localDb;
          this.databases.set(dbKey, dbConfig);
          return { success: true, repaired: true };
        } else if (dbConfig.lanDb) {
          console.log(`[DatabaseHandler] Primary database missing for ${dbKey}, using lanDb as fallback`);
          dbConfig.db = dbConfig.lanDb;
          this.databases.set(dbKey, dbConfig);
          return { success: true, repaired: true };
        }
      }

      // Configuration looks valid
      console.log(`[DatabaseHandler] Database config for ${dbKey} is valid`);
      return { success: true, repaired: false };

    } catch (error) {
      console.error(`[DatabaseHandler] Error validating/repairing database config for ${dbKey}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update the current user context (called when user logs in)
   */
  setCurrentUser(user) {
    if (user && typeof user === 'object' && user.label && user.value) {
      // Only store essential fields: label (name) and value (user ID)
      this.currentUser = {
        label: user.label,
        value: user.value
      };
      console.log(`[DatabaseHandler] Current user updated: ${user.label} (${user.value})`);
    } else {
      console.warn(`[DatabaseHandler] Invalid user object provided:`, user);
    }
  }

  /**
   * Get the current user context
   */
  getCurrentUser() {
    return this.currentUser;
  }

  // Database info method
  async info(dbKey) {
    try {
      const dbConfig = this.databases.get(dbKey);
      if (!dbConfig) {
        throw new Error(`Database ${dbKey} not initialized`);
      }
      return await dbConfig.db.info();
    } catch (error) {
      throw error;
    }
  }

  // Logs methods
  async getLogs(databasePrefix, options = {}) {
    try {
      const logsDbKey = databasePrefix ? `${databasePrefix}logs` : 'logs';

      // Initialize logs database if not exists
      if (!this.databases.has(logsDbKey)) {
        await this.initializeDatabase('logs', databasePrefix, null, 'none');
      }

      const logs = await this.getAllDocuments(logsDbKey, options);

      // Debug: Check what we're returning
      console.log(`[DatabaseHandler] Retrieved ${logs.length} logs from ${logsDbKey}`);
      if (logs.length > 0) {
        console.log(`[DatabaseHandler] Sample log details:`, JSON.stringify(logs[0].details));
      }

      return logs;
    } catch (error) {
      console.error(`[DatabaseHandler] Failed to get logs:`, error);
      return [];
    }
  }

  // Sync methods to match CapacitorDB
  async instantSync(dbKey) {
    return { success: true };
  }

  async syncToRemote(dbKey) {
    return this.syncThis(dbKey, `https://therick:<EMAIL>/${dbKey}`);
  }

  async syncToLan(dbKey, lanUrl) {
    return this.syncThis(dbKey, lanUrl);
  }

  // Event emission for compatibility
  emitDocumentChanged(data) {
    this.emit('documentChanged', data);
  }

  /**
   * PERFORMANCE FIX: Enhanced cleanup with connection pooling and sync interval management
   */
  destroy() {
    // PERFORMANCE FIX: Cleanup connection pooling
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Close all pooled connections
    for (const [dbKey, pool] of this.connectionPool) {
      for (const connection of pool) {
        try {
          if (connection.db && typeof connection.db.close === 'function') {
            connection.db.close().catch(() => { });
          }
        } catch (error) {
          console.warn(`[DatabaseHandler] Error closing pooled connection for ${dbKey}:`, error);
        }
      }
    }
    this.connectionPool.clear();
    this.connectionStats.clear();

    for (const [dbKey, config] of this.databases) {
      // Clear sync intervals
      if (config.syncInterval) {
        clearInterval(config.syncInterval);
      }

      // Clean up change listeners
      this.cleanupChangeListeners(dbKey);

      // Close databases
      if (config.db && config.db.close) {
        config.db.close().catch(() => { });
      }
      if (config.localDb && config.localDb !== config.db && config.localDb.close) {
        config.localDb.close().catch(() => { });
      }
      if (config.lanDb && config.lanDb !== config.db && config.lanDb.close) {
        config.lanDb.close().catch(() => { });
      }
    }

    // Clean up remote server monitoring
    if (this.remoteServerMonitoringInterval) {
      clearInterval(this.remoteServerMonitoringInterval);
      this.remoteServerMonitoringInterval = null;
    }

    this.databases.clear();
    this.removeAllListeners();
  }

  /**
   * Check if remote server is available
   */
  async checkRemoteServerAvailability() {
    const testUrl = 'https://therick:<EMAIL>/';
    try {
      // Create a temporary PouchDB instance to test connection
      const testDb = new PatchedPouchDBWithFind(testUrl);

      // Test with a short timeout
      await Promise.race([
        testDb.info(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Remote server timeout')), 5000))
      ]);

      // Clean up test connection
      await testDb.close();

      console.log('[DatabaseHandler] Remote server is available');
      return { available: true, url: testUrl };
    } catch (error) {
      console.warn('[DatabaseHandler] Remote server is unavailable:', error.message);
      return { available: false, url: testUrl, error: error.message };
    }
  }

  /**
   * Enable or disable remote sync based on server availability
   */
  async updateRemoteSyncStatus() {
    const availability = await this.checkRemoteServerAvailability();

    // Update all database configurations
    for (const [dbKey, config] of this.databases) {
      config.remoteSyncEnabled = availability.available;
      if (!availability.available) {
        console.log(`[DatabaseHandler] Remote sync disabled for ${dbKey} - server unavailable`);
      }
    }

    // Emit status change event
    this.emit('remoteSyncStatusChanged', {
      available: availability.available,
      timestamp: new Date().toISOString(),
      error: availability.error
    });

    return availability;
  }

  /**
   * Get current remote sync status for a database
   */
  getRemoteSyncStatus(dbKey) {
    const config = this.databases.get(dbKey);
    if (!config) return { enabled: false, reason: 'Database not found' };

    return {
      enabled: config.remoteSyncEnabled !== false, // Default to true if not explicitly disabled
      available: config.remoteSyncEnabled,
      dbKey: dbKey
    };
  }

  /**
   * Manually enable/disable remote sync for a database
   */
  setRemoteSyncEnabled(dbKey, enabled) {
    const config = this.databases.get(dbKey);
    if (!config) {
      throw new Error(`Database ${dbKey} not found`);
    }

    config.remoteSyncEnabled = enabled;
    console.log(`[DatabaseHandler] Remote sync ${enabled ? 'enabled' : 'disabled'} for ${dbKey}`);

    this.emit('remoteSyncStatusChanged', {
      dbKey: dbKey,
      enabled: enabled,
      timestamp: new Date().toISOString()
    });

    return { success: true, dbKey, enabled };
  }

  /**
   * Track sync performance metrics
   */
  trackSyncPerformance(dbKey, startTime, endTime, syncType) {
    const duration = endTime - startTime;

    if (!this.syncMetrics) {
      this.syncMetrics = new Map();
    }

    if (!this.syncMetrics.has(dbKey)) {
      this.syncMetrics.set(dbKey, {
        totalSyncs: 0,
        totalDuration: 0,
        averageDuration: 0,
        lastSyncTime: 0,
        syncTypes: {}
      });
    }

    const metrics = this.syncMetrics.get(dbKey);
    metrics.totalSyncs++;
    metrics.totalDuration += duration;
    metrics.averageDuration = metrics.totalDuration / metrics.totalSyncs;
    metrics.lastSyncTime = endTime;

    if (!metrics.syncTypes[syncType]) {
      metrics.syncTypes[syncType] = { count: 0, totalDuration: 0 };
    }
    metrics.syncTypes[syncType].count++;
    metrics.syncTypes[syncType].totalDuration += duration;

    // Log slow syncs for performance monitoring
    if (duration > 10000) { // 10 seconds
      console.warn(`[DatabaseHandler] Slow sync detected for ${dbKey}: ${duration}ms (${syncType})`);
    }
  }

  /**
   * Get sync performance metrics
   */
  getSyncMetrics(dbKey = null) {
    if (!this.syncMetrics) return {};

    if (dbKey) {
      return this.syncMetrics.get(dbKey) || {};
    }

    return Object.fromEntries(this.syncMetrics);
  }

  /**
   * Setup change listeners for automatic conflict detection and resolution
   */
  setupChangeListeners(dbKey) {
    const config = this.databases.get(dbKey);
    if (!config) {
      console.error(`[DatabaseHandler] Cannot setup change listeners for ${dbKey}: database not found`);
      return;
    }

    try {
      // Setup change listener for primary database
      const primaryDb = config.isPrimaryLan ? config.lanDb : config.localDb;
      if (primaryDb) {
        this.setupDatabaseChangeListener(primaryDb, dbKey, 'primary');
      }

      // Setup change listener for local database if different from primary
      if (config.localDb && config.localDb !== primaryDb) {
        this.setupDatabaseChangeListener(config.localDb, dbKey, 'local');
      }

      // Setup change listener for LAN database if different from primary
      if (config.lanDb && config.lanDb !== primaryDb) {
        this.setupDatabaseChangeListener(config.lanDb, dbKey, 'lan');
      }

      console.log(`[DatabaseHandler] Change listeners setup completed for ${dbKey}`);
    } catch (error) {
      console.error(`[DatabaseHandler] Failed to setup change listeners for ${dbKey}:`, error.message);
    }
  }

  /**
   * Setup change listener for a specific database
   */
  setupDatabaseChangeListener(db, dbKey, type) {
    try {
      // MEMORY LEAK FIX: Set max listeners on database
      if (db.setMaxListeners) {
        db.setMaxListeners(100);
      }

      const changeListener = db.changes({
        since: 'now',
        live: true,
        include_docs: true,
        conflicts: false // DISABLED: Conflict detection disabled for maximum performance
      });

      // MEMORY LEAK FIX: Set max listeners on change listener
      if (changeListener.setMaxListeners) {
        changeListener.setMaxListeners(50);
      }

      const changeHandler = (change) => {
        // Skip design documents
        if (change.id && change.id.startsWith('_design/')) return;

        // DISABLED: Conflict detection disabled for maximum performance
        // if (change.doc && change.doc._conflicts && change.doc._conflicts.length > 0) {
        //   console.log(`[DatabaseHandler] Conflict detected in ${type} database for ${dbKey}: ${change.id}`);
        //   this.handleConflicts(change);
        // }

        // Emit document change event for real-time updates
        this.emit('documentChanged', {
          type: change.deleted ? 'delete' : (change.doc._rev.startsWith('1-') ? 'create' : 'update'),
          dbKey: dbKey,
          id: change.id,
          doc: change.doc,
          deleted: change.deleted,
          source: type
        });
      };

      const errorHandler = (error) => {
        console.error(`[DatabaseHandler] Change listener error for ${dbKey} (${type}):`, error);
      };

      // Add listeners
      changeListener.on('change', changeHandler);
      changeListener.on('error', errorHandler);

      // Store listener for cleanup
      if (!this.activeChangeListeners) {
        this.activeChangeListeners = new Map();
      }

      this.activeChangeListeners.set(`${dbKey}_${type}`, {
        listener: changeListener,
        changeHandler,
        errorHandler,
        dbKey,
        type
      });

      console.log(`[DatabaseHandler] Change listener setup for ${dbKey} (${type})`);
    } catch (error) {
      console.error(`[DatabaseHandler] Failed to setup change listener for ${dbKey} (${type}):`, error.message);
    }
  }

  /**
   * Setup change listeners for all databases
   */
  setupAllChangeListeners() {
    console.log(`[DatabaseHandler] Setting up change listeners for all databases...`);

    for (const [dbKey, config] of this.databases) {
      this.setupChangeListeners(dbKey);
    }

    console.log(`[DatabaseHandler] Change listeners setup completed for all databases`);
  }

  /**
   * Clean up change listeners for a specific database
   */
  cleanupChangeListeners(dbKey) {
    if (!this.activeChangeListeners) return;

    const keysToRemove = [];
    for (const [key, listenerData] of this.activeChangeListeners) {
      if (listenerData.dbKey === dbKey) {
        try {
          // Remove listeners
          if (listenerData.listener) {
            if (listenerData.changeHandler) {
              listenerData.listener.removeListener('change', listenerData.changeHandler);
            }
            if (listenerData.errorHandler) {
              listenerData.listener.removeListener('error', listenerData.errorHandler);
            }
            listenerData.listener.removeAllListeners();
            listenerData.listener.cancel();
          }
        } catch (error) {
          console.warn(`[DatabaseHandler] Error cleaning up change listener for ${key}:`, error.message);
        }
        keysToRemove.push(key);
      }
    }

    // Remove from map
    keysToRemove.forEach(key => this.activeChangeListeners.delete(key));
    console.log(`[DatabaseHandler] Cleaned up change listeners for ${dbKey}`);
  }

  /**
   * Get conflict resolution statistics
   */
  getConflictStats() {
    if (this.conflictResolver) {
      return this.conflictResolver.getStats();
    }
    return { error: 'ConflictResolver not initialized' };
  }


}

module.exports = DatabaseHandler;
module.exports.PouchDB = PatchedPouchDBWithFind;