const { ipcMain, dialog, shell, app, BrowserWindow } = require('electron');
const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const DatabaseHandler = require('./database/DatabaseHandler');
const SyncService = require('./database/SyncService');

/**
 * PERFORMANCE & SECURITY: Enhanced IPC handlers for Electron main process
 * Provides secure communication between renderer and main process
 */

class IPCHandlers {
  constructor() {
    // Initialize database services
    this.databaseHandler = new DatabaseHandler();
    this.syncService = new SyncService(this.databaseHandler);

    // Debug: Check if getAllDocuments method exists
    console.log('[IPCHandlers] DatabaseHandler methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.databaseHandler)));
    console.log('[IPCHandlers] getAllDocuments exists:', typeof this.databaseHandler.getAllDocuments === 'function');

    this.setupHandlers();
    this.setupDatabaseHandlers();
  }

  setupHandlers() {
    // App information handlers
    ipcMain.handle('get-app-version', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
        chromeVersion: process.versions.chrome
      };
    });

    // System information handlers
    ipcMain.handle('get-system-info', () => {
      return {
        platform: process.platform,
        arch: process.arch,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        uptime: os.uptime(),
        userInfo: os.userInfo()
      };
    });

    // Memory usage monitoring
    ipcMain.handle('get-memory-usage', () => {
      const memoryUsage = process.memoryUsage();
      return {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) // MB
      };
    });

    // Window control handlers
    ipcMain.handle('minimize-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) win.minimize();
    });

    ipcMain.handle('maximize-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) {
        if (win.isMaximized()) {
          win.unmaximize();
        } else {
          win.maximize();
        }
      }
    });

    ipcMain.handle('close-window', (event) => {
      const win = BrowserWindow.fromWebContents(event.sender);
      if (win) win.close();
    });

    // Secure email handler
    ipcMain.handle('send-email', async (event, emailData) => {
      try {
        const { from, to, subject, html } = emailData;

        const transporter = nodemailer.createTransport({
          host: 'smtp.yandex.com',
          port: 465,
          secure: true,
          auth: {
            user: '<EMAIL>',
            pass: '@*********' // Consider using environment variables
          },
          tls: {
            rejectUnauthorized: false
          }
        });

        const mailOptions = {
          from: from || '"Haclab Company Limited" <<EMAIL>>',
          to: to,
          subject: subject,
          html: html
        };

        const info = await transporter.sendMail(mailOptions);
        return { success: true, messageId: info.messageId };
      } catch (error) {
        console.error('Email sending failed:', error);
        return { success: false, error: error.message };
      }
    });

    // Secure file operations
    ipcMain.handle('select-file', async (event, options = {}) => {
      try {
        const result = await dialog.showOpenDialog({
          properties: ['openFile'],
          filters: options.filters || [
            { name: 'All Files', extensions: ['*'] }
          ],
          ...options
        });

        if (!result.canceled && result.filePaths.length > 0) {
          const filePath = result.filePaths[0];
          const stats = await fs.stat(filePath);

          return {
            success: true,
            filePath: filePath,
            fileName: path.basename(filePath),
            fileSize: stats.size,
            lastModified: stats.mtime
          };
        }

        return { success: false, canceled: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('save-file', async (event, data) => {
      try {
        const { content, defaultPath, filters } = data;

        const result = await dialog.showSaveDialog({
          defaultPath: defaultPath,
          filters: filters || [
            { name: 'All Files', extensions: ['*'] }
          ]
        });

        if (!result.canceled && result.filePath) {
          await fs.writeFile(result.filePath, content, 'utf8');
          return { success: true, filePath: result.filePath };
        }

        return { success: false, canceled: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });



    // External link handler
    ipcMain.handle('open-external', async (event, url) => {
      try {
        await shell.openExternal(url);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // App restart handler
    ipcMain.handle('restart-app', () => {
      app.relaunch();
      app.exit();
    });

    // Clear cache handler
    ipcMain.handle('clear-cache', async (event) => {
      try {
        const win = BrowserWindow.fromWebContents(event.sender);
        if (win) {
          await win.webContents.session.clearCache();
          await win.webContents.session.clearStorageData();
          return { success: true };
        }
        return { success: false, error: 'Window not found' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    console.log('✅ IPC Handlers initialized successfully');
  }

  setupDatabaseHandlers() {
    // Database initialization
    ipcMain.handle('db-initialize', async (event, { name, databasePrefix, lan_details, branch }) => {
      return await this.databaseHandler.initializeDatabase(name, databasePrefix, lan_details, branch);
    });

    // Ensure mission control databases are initialized
    ipcMain.handle('db-ensure-mission-control', async (event, { lan_details, branch }) => {
      return await this.databaseHandler.ensureAllMissionControlDatabases(lan_details, branch);
    });

    // Debug: List all documents in mission_control_organizations
    ipcMain.handle('db-debug-mission-control-organizations', async (event) => {
      try {
        const dbKey = 'mission_control_organizations';
        const dbConfig = this.databaseHandler.databases.get(dbKey);

        if (!dbConfig) {
          return { success: false, error: 'Mission control organizations database not initialized' };
        }

        const allDocs = await dbConfig.db.allDocs({ include_docs: true });
        const organizations = allDocs.rows
          .filter(row => !row.id.startsWith('_'))
          .map(row => ({
            id: row.doc._id,
            name: row.doc.name || 'No name',
            rev: row.doc._rev
          }));

        console.log(`[DEBUG] Found ${organizations.length} organizations in mission_control_organizations:`, organizations);

        return {
          success: true,
          count: organizations.length,
          organizations: organizations
        };
      } catch (error) {
        console.error('[DEBUG] Error listing mission control organizations:', error);
        return { success: false, error: error.message };
      }
    });

    // Create organization in mission_control_organizations if it doesn't exist
    ipcMain.handle('db-ensure-organization', async (event, { organizationId, organizationData }) => {
      try {
        const dbKey = 'mission_control_organizations';
        const dbConfig = this.databaseHandler.databases.get(dbKey);

        if (!dbConfig) {
          // Try to initialize the database first
          await this.databaseHandler.ensureAllMissionControlDatabases();
          const retryDbConfig = this.databaseHandler.databases.get(dbKey);
          if (!retryDbConfig) {
            return { success: false, error: 'Could not initialize mission control organizations database' };
          }
        }

        const finalDbConfig = this.databaseHandler.databases.get(dbKey);

        // Check if organization already exists
        try {
          const existingOrg = await finalDbConfig.db.get(organizationId.toUpperCase());
          console.log(`[DEBUG] Organization ${organizationId} already exists:`, existingOrg.name);
          return { success: true, exists: true, organization: existingOrg };
        } catch (error) {
          if (error.status !== 404) {
            throw error; // Re-throw if it's not a "not found" error
          }
        }

        // Create the organization document
        const orgDoc = {
          _id: organizationId.toUpperCase(),
          name: organizationData.name || `Organization ${organizationId}`,
          created: new Date().toISOString(),
          ...organizationData
        };

        const result = await finalDbConfig.db.put(orgDoc);
        console.log(`[DEBUG] Created organization ${organizationId} in mission_control_organizations`);

        return {
          success: true,
          created: true,
          organization: { ...orgDoc, _rev: result.rev }
        };
      } catch (error) {
        console.error('[DEBUG] Error ensuring organization:', error);
        return { success: false, error: error.message };
      }
    });

    // Repair database configuration
    ipcMain.handle('db-repair-config', async (event, { dbKey }) => {
      try {
        const dbConfig = this.databaseHandler.databases.get(dbKey);
        if (!dbConfig) {
          return { success: false, error: `Database ${dbKey} not found` };
        }

        const repairResult = await this.databaseHandler.validateAndRepairDatabaseConfig(dbKey, dbConfig);
        console.log(`[DEBUG] Database repair result for ${dbKey}:`, repairResult);

        return repairResult;
      } catch (error) {
        console.error('[DEBUG] Error repairing database config:', error);
        return { success: false, error: error.message };
      }
    });

    // CRUD operations
    ipcMain.handle('db-save', async (event, { dbKey, data, user }) => {
      try {
        console.log(`[IPCHandlers] db-save called for ${dbKey} with data:`, data._id || 'new document');
        const result = await this.databaseHandler.saveDocument(dbKey, data, user);

        // PERFORMANCE FIX: Optimize return data to reduce IPC overhead
        const optimizedResult = {
          success: true,
          id: result.id,
          // Only include essential document fields to reduce transfer size
          doc: result.doc ? {
            _id: result.doc._id,
            _rev: result.doc._rev,
            updatedAt: result.doc.updatedAt,
            createdAt: result.doc.createdAt
          } : null
        };

        console.log(`[IPCHandlers] db-save result:`, { success: true, id: result.id, hasDoc: !!result.doc });

        // Ensure we always return the expected format
        if (result.success === false) {
          // Handle error case from saveDocument
          return { success: false, error: result.error, id: result.id, doc: null };
        }

        return optimizedResult;
      } catch (error) {
        console.error(`[IPCHandlers] db-save error for ${dbKey}:`, error.message);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get', async (event, { dbKey, id }) => {
      try {
        const doc = await this.databaseHandler.getDocument(dbKey, id);
        return { success: true, doc };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get-all', async (event, { dbKey, options }) => {
      try {
        console.log('[IPCHandlers] db-get-all called for:', dbKey, 'with options:', options);
        console.log('[IPCHandlers] getAllDocuments method exists:', typeof this.databaseHandler.getAllDocuments === 'function');

        if (typeof this.databaseHandler.getAllDocuments !== 'function') {
          console.error('[IPCHandlers] getAllDocuments method not found! Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.databaseHandler)));
          return { success: false, error: 'getAllDocuments method not found' };
        }

        // PERFORMANCE FIX: Add timeout to prevent IPC hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`IPC timeout for db-get-all: ${dbKey}`)), 45000); // 45 second timeout
        });

        const queryPromise = this.databaseHandler.getAllDocuments(dbKey, options);
        const docs = await Promise.race([queryPromise, timeoutPromise]);

        console.log(`[IPCHandlers] db-get-all returned ${docs.length} documents for ${dbKey}`);
        return { success: true, docs };
      } catch (error) {
        console.error('[IPCHandlers] db-get-all error:', error);
        return { success: false, error: error.message };
      }
    });

    // PERFORMANCE FIX: Add document count handler for pagination
    ipcMain.handle('db-get-count', async (event, { dbKey, options }) => {
      try {
        console.log('[IPCHandlers] db-get-count called for:', dbKey, 'with options:', options);
        const count = await this.databaseHandler.getDocumentCount(dbKey, options);
        console.log(`[IPCHandlers] db-get-count returned ${count} for ${dbKey}`);
        return { success: true, count };
      } catch (error) {
        console.error('[IPCHandlers] db-get-count error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-delete', async (event, { dbKey, id, user }) => {
      try {
        const result = await this.databaseHandler.deleteDocument(dbKey, id, user);
        return { success: true, id: result.id, rev: result.rev };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-bulk-save', async (event, { dbKey, docs, user }) => {
      try {
        console.log(`[IPCHandlers] db-bulk-save called for ${dbKey} with ${docs.length} documents`);
        const results = await this.databaseHandler.bulkSave(dbKey, docs, user);

        // PERFORMANCE FIX: Optimize bulk results to reduce IPC overhead
        const optimizedResults = results.map(result => ({
          success: result.success,
          id: result.id,
          rev: result.rev,
          error: result.error
          // Don't include full document to reduce transfer size
        }));

        console.log(`[IPCHandlers] db-bulk-save completed: ${optimizedResults.filter(r => r.success).length}/${docs.length} successful`);
        return { success: true, results: optimizedResults };
      } catch (error) {
        console.error(`[IPCHandlers] db-bulk-save error for ${dbKey}:`, error.message);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-find', async (event, { dbKey, selector, options }) => {
      try {
        console.log(`[IPCHandlers] db-find called for ${dbKey} with selector:`, selector, 'options:', options);
        const docs = await this.databaseHandler.findDocuments(dbKey, selector, options);
        console.log(`[IPCHandlers] db-find returned ${docs.length} documents for ${dbKey}`);
        return { success: true, docs };
      } catch (error) {
        console.error(`[IPCHandlers] db-find error for ${dbKey}:`, error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-all-docs', async (event, { dbKey, options }) => {
      try {
        const result = await this.databaseHandler.allDocs(dbKey, options);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Sync operations
    ipcMain.handle('db-sync-this', async (event, { dbKey, targetUrl, options }) => {
      try {
        const result = await this.databaseHandler.syncThis(dbKey, targetUrl, options);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-start', async (event, { databases }) => {
      return await this.syncService.startSyncService(databases);
    });

    ipcMain.handle('sync-stop', async () => {
      return await this.syncService.stopSyncService();
    });

    ipcMain.handle('sync-force-full', async () => {
      return await this.syncService.forceFullSync();
    });

    ipcMain.handle('sync-lan-specific', async (event, { dbKey, lanUrl }) => {
      try {
        const result = await this.syncService.performSpecificLanSync(dbKey, lanUrl);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-status', async () => {
      return this.syncService.getSyncStatus();
    });

    ipcMain.handle('sync-add-database', async (event, { dbKey }) => {
      return await this.syncService.addDatabase(dbKey);
    });

    ipcMain.handle('sync-remove-database', async (event, { dbKey }) => {
      return await this.syncService.removeDatabase(dbKey);
    });

    ipcMain.handle('sync-restart-database', async (event, { dbKey }) => {
      try {
        // Stop and restart the database sync
        this.syncService.stopDatabaseSync(dbKey);
        this.syncService.startDatabaseSync(dbKey);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-update-lan-details', async (event, { dbKey, lan_details }) => {
      try {
        const result = await this.databaseHandler.updateLanDetails(dbKey, lan_details);

        // If LAN details were updated successfully, restart sync for this database
        if (result.success) {
          // Stop and restart the database sync with new LAN details
          this.syncService.stopDatabaseSync(dbKey);
          this.syncService.startDatabaseSync(dbKey);
          console.log(`[IPCHandlers] Sync restarted for ${dbKey} after LAN details update`);
        }

        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-trigger-instant', async (event, { dbKey, operationType }) => {
      try {
        // Trigger both LAN and remote instant sync
        await this.syncService.performInstantLanSync(dbKey);

        // Add a small delay before remote sync to prevent overwhelming
        setTimeout(() => {
          this.syncService.performInstantRemoteSync(dbKey);
        }, 500);

        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // CRUD OPERATION SYNC CONTROL: IPC handlers for sync control during CRUD operations
    ipcMain.handle('sync-start-crud-operation', async (event, { dbKey, operationType }) => {
      try {
        if (this.syncService) {
          this.syncService.startCrudOperation(dbKey, operationType);
          return { success: true };
        }
        return { success: false, error: 'Sync service not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-end-crud-operation', async (event, { dbKey, operationType }) => {
      try {
        if (this.syncService) {
          this.syncService.endCrudOperation(dbKey, operationType);
          return { success: true };
        }
        return { success: false, error: 'Sync service not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-pause-database', async (event, { dbKey, reason }) => {
      try {
        if (this.syncService) {
          this.syncService.pauseSyncForDatabase(dbKey, reason);
          return { success: true };
        }
        return { success: false, error: 'Sync service not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-resume-database', async (event, { dbKey, reason }) => {
      try {
        if (this.syncService) {
          this.syncService.resumeSyncForDatabase(dbKey, reason);
          return { success: true };
        }
        return { success: false, error: 'Sync service not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-get-crud-status', async (event) => {
      try {
        if (this.syncService) {
          return {
            success: true,
            status: this.syncService.getCrudOperationStatus()
          };
        }
        return { success: false, error: 'Sync service not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // SIMPLE CONFLICT RESOLUTION CONTROL: IPC handlers
    ipcMain.handle('conflict-enable', async () => {
      try {
        if (this.databaseHandler.conflictResolver) {
          this.databaseHandler.conflictResolver.setEnabled(true);
          return { success: true };
        }
        return { success: false, error: 'Conflict resolver not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('conflict-disable', async () => {
      try {
        if (this.databaseHandler.conflictResolver) {
          this.databaseHandler.conflictResolver.setEnabled(false);
          return { success: true };
        }
        return { success: false, error: 'Conflict resolver not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('conflict-get-stats', async () => {
      try {
        if (this.databaseHandler.conflictResolver) {
          const stats = this.databaseHandler.conflictResolver.getStats();
          return { success: true, stats };
        }
        return { success: false, error: 'Conflict resolver not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('conflict-clear-queue', async () => {
      try {
        if (this.databaseHandler.conflictResolver) {
          this.databaseHandler.conflictResolver.clearQueue();
          return { success: true };
        }
        return { success: false, error: 'Conflict resolver not available' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-trigger', async (event, { dbKey }) => {
      try {
        const result = await this.syncService.forceSyncDatabase(dbKey);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-set-remote-url', async (event, { url }) => {
      try {
        this.syncService.setRemoteConnectionString(url);
        return { success: true };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('sync-test-remote-connectivity', async () => {
      return { success: false, error: 'Remote connectivity test removed in simplified version' };
    });

    ipcMain.handle('sync-get-remote-url', async () => {
      return { success: true, url: 'https://therick:<EMAIL>/' };
    });

    // Database management
    ipcMain.handle('db-close', async (event, { dbKey }) => {
      return await this.databaseHandler.closeDatabase(dbKey);
    });

    ipcMain.handle('db-close-all', async () => {
      return await this.databaseHandler.closeAllDatabases();
    });

    ipcMain.handle('db-get-all-databases', async () => {
      return this.databaseHandler.getAllDatabases();
    });

    // Attachment operations
    ipcMain.handle('db-get-attachment', async (event, { dbKey, id, name }) => {
      try {
        const attachment = await this.databaseHandler.getAttachment(dbKey, id, name);
        // getAttachment returns null for not found, which is a valid response
        return { success: true, attachment };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-put-attachment', async (event, { dbKey, id, name, rev, attachment, type }) => {
      try {
        const result = await this.databaseHandler.putAttachment(dbKey, id, name, rev, attachment, type);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-remove-attachment', async (event, { dbKey, id, name, rev }) => {
      try {
        const result = await this.databaseHandler.removeAttachment(dbKey, id, name, rev);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Additional database operations
    ipcMain.handle('db-authenticate', async (event, { dbKey, username, password }) => {
      try {
        const result = await this.databaseHandler.authenticate(dbKey, username, password);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-dump', async (event, { dbKey }) => {
      return { success: false, error: 'Database dump method removed in simplified version' };
    });

    ipcMain.handle('db-create-index', async (event, { dbKey, index }) => {
      try {
        const result = await this.databaseHandler.createIndex(dbKey, index);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-info', async (event, { dbKey }) => {
      return { success: false, error: 'Database info method removed in simplified version' };
    });

    ipcMain.handle('db-compact', async (event, { dbKey }) => {
      return { success: false, error: 'Database compact method removed in simplified version' };
    });

    ipcMain.handle('db-sync-stats', async (event, { dbKey }) => {
      try {
        const stats = await this.databaseHandler.getSyncStats(dbKey);
        return { success: true, stats };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-force-sync', async (event, { dbKey }) => {
      try {
        const result = await this.databaseHandler.forceSync(dbKey);
        return { success: true, result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Logging operations
    ipcMain.handle('db-add-log', async (event, { databasePrefix, logData }) => {
      try {
        const result = await this.databaseHandler.addLog(databasePrefix, logData);
        return result;
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Get logs from logs database
    ipcMain.handle('db-get-logs', async (event, { databasePrefix, options }) => {
      try {
        console.log(`[IPCHandlers] db-get-logs called for prefix: ${databasePrefix}`);
        const logs = await this.databaseHandler.getLogs(databasePrefix, options);
        console.log(`[IPCHandlers] Retrieved ${logs.length} logs`);
        return { success: true, logs };
      } catch (error) {
        console.error(`[IPCHandlers] db-get-logs error:`, error);
        return { success: false, error: error.message };
      }
    });

    // Real-time updates - forward database changes to renderer
    this.databaseHandler.on('documentChanged', (data) => {
      // Broadcast to all renderer processes
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('db-document-changed', data);
      });
    });

    // BETTER IMPLEMENTATION: Direct PouchDB change listeners with proper cleanup
    this.activeChangeListeners = new Map();

    ipcMain.handle('db-setup-change-listener', async (event, { dbKey }) => {
      try {
        console.log(`[IPCHandlers] Setting up direct change listener for ${dbKey}`);

        // RACE CONDITION FIX: Wait for database to be initialized if not found
        let dbConfig = this.databaseHandler.databases.get(dbKey);
        if (!dbConfig) {
          console.log(`[IPCHandlers] Database ${dbKey} not found, waiting for initialization...`);

          // Wait up to 5 seconds for database to be initialized
          const maxWaitTime = 5000;
          const checkInterval = 100;
          let waitTime = 0;

          while (!dbConfig && waitTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitTime += checkInterval;
            dbConfig = this.databaseHandler.databases.get(dbKey);
          }

          if (!dbConfig) {
            throw new Error(`Database ${dbKey} not found after waiting ${maxWaitTime}ms`);
          }

          console.log(`[IPCHandlers] Database ${dbKey} found after waiting ${waitTime}ms`);
        }

        // Remove existing listener if any
        if (this.activeChangeListeners.has(dbKey)) {
          const existingListener = this.activeChangeListeners.get(dbKey);
          console.log(`[IPCHandlers] Removing existing listener for ${dbKey}`);

          // MEMORY LEAK FIX: Properly remove all event listeners before cancelling
          try {
            if (existingListener.listener) {
              existingListener.listener.removeAllListeners('change');
              existingListener.listener.removeAllListeners('error');
              existingListener.listener.removeAllListeners('complete');
              existingListener.listener.cancel();
            }
          } catch (cancelError) {
            console.warn(`[IPCHandlers] Error cancelling existing listener for ${dbKey}:`, cancelError);
          }

          this.activeChangeListeners.delete(dbKey);
        }

        // Set up PouchDB change listener on the primary database
        const primaryDb = dbConfig.db;

        // MEMORY LEAK FIX: Increase max listeners on the database to prevent warnings
        if (primaryDb.setMaxListeners) {
          primaryDb.setMaxListeners(50);
        }

        const changeListener = primaryDb.changes({
          since: 'now',
          live: true,
          include_docs: true
        });

        // Store bound handlers for proper cleanup
        const changeHandler = (change) => {
          // Skip design documents
          if (change.id && change.id.startsWith('_design/')) return;

          console.log(`[IPCHandlers] Direct change detected for ${dbKey}:`, change.id);

          // Broadcast change to all renderer processes
          BrowserWindow.getAllWindows().forEach(win => {
            win.webContents.send('db-change-detected', {
              dbKey,
              type: change.deleted ? 'delete' : (change.doc._rev.startsWith('1-') ? 'create' : 'update'),
              id: change.id,
              doc: change.doc,
              deleted: change.deleted
            });
          });
        };

        const errorHandler = (error) => {
          console.error(`[IPCHandlers] Change listener error for ${dbKey}:`, error);
        };

        // Add listeners with stored references
        changeListener.on('change', changeHandler);
        changeListener.on('error', errorHandler);

        // Store both the listener and handlers for proper cleanup
        this.activeChangeListeners.set(dbKey, {
          listener: changeListener,
          changeHandler,
          errorHandler
        });

        return { success: true };
      } catch (error) {
        console.error(`[IPCHandlers] Failed to setup change listener for ${dbKey}:`, error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-remove-change-listener', async (event, { dbKey }) => {
      try {
        if (this.activeChangeListeners.has(dbKey)) {
          const listenerData = this.activeChangeListeners.get(dbKey);
          console.log(`[IPCHandlers] Removing change listener for ${dbKey}`);

          // MEMORY LEAK FIX: Properly remove event listeners before cancelling
          try {
            if (listenerData.listener) {
              // Remove specific handlers
              if (listenerData.changeHandler) {
                listenerData.listener.removeListener('change', listenerData.changeHandler);
              }
              if (listenerData.errorHandler) {
                listenerData.listener.removeListener('error', listenerData.errorHandler);
              }

              // Remove any remaining listeners
              listenerData.listener.removeAllListeners('change');
              listenerData.listener.removeAllListeners('error');
              listenerData.listener.removeAllListeners('complete');

              // Cancel the listener
              listenerData.listener.cancel();
            }
          } catch (cleanupError) {
            console.warn(`[IPCHandlers] Error during listener cleanup for ${dbKey}:`, cleanupError);
          }

          this.activeChangeListeners.delete(dbKey);
          console.log(`[IPCHandlers] Successfully removed change listener for ${dbKey}`);
        }
        return { success: true };
      } catch (error) {
        console.error(`[IPCHandlers] Failed to remove change listener for ${dbKey}:`, error);
        return { success: false, error: error.message };
      }
    });

    // Forward sync events to renderer
    this.syncService.on('syncComplete', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-complete', data);
      });
    });

    this.syncService.on('syncError', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-error', data);
      });
    });

    this.syncService.on('syncProgress', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('sync-progress', data);
      });
    });

    // User management handlers
    ipcMain.handle('db-set-current-user', async (event, { user }) => {
      try {
        console.log(`[IPCHandlers] Setting current user:`, user?.label || user?.name || user?.key);
        this.databaseHandler.setCurrentUser(user);
        return { success: true };
      } catch (error) {
        console.error(`[IPCHandlers] Failed to set current user:`, error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get-current-user', async (event) => {
      try {
        const user = this.databaseHandler.getCurrentUser();
        return { success: true, user };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Essential databases management
    ipcMain.handle('db-ensure-essential', async (event, { databaseName, organizationPrefix, lan_details, branch }) => {
      try {
        console.log(`[IPCHandlers] Ensuring essential database: ${databaseName} with prefix: ${organizationPrefix || 'none'}`);
        const result = await this.databaseHandler.ensureEssentialDatabase(databaseName, organizationPrefix, lan_details, branch);
        return { success: true, ...result };
      } catch (error) {
        console.error(`[IPCHandlers] Failed to ensure essential database:`, error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('db-get-essential-info', async (event, { databaseName }) => {
      try {
        const info = this.databaseHandler.getEssentialDatabaseInfo(databaseName);
        return { success: true, info };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Forward conflict resolution events to renderer
    this.databaseHandler.on('conflictQueued', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('conflict-queued', data);
      });
    });

    this.databaseHandler.on('conflictResolved', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('conflict-resolved', data);
      });
    });

    this.databaseHandler.on('potentialConflictDetected', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('potential-conflict-detected', data);
      });
    });

    this.databaseHandler.on('conflictResolutionError', (data) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send('conflict-resolution-error', data);
      });
    });

    // Conflict resolution IPC handlers
    ipcMain.handle('get-conflict-stats', async () => {
      try {
        return this.databaseHandler.getConflictStats();
      } catch (error) {
        console.error('[IPCHandlers] Failed to get conflict stats:', error);
        return { error: error.message };
      }
    });

    ipcMain.handle('force-resolve-all-conflicts', async (event, { dbKey }) => {
      try {
        return await this.databaseHandler.forceResolveAllConflicts(dbKey);
      } catch (error) {
        console.error(`[IPCHandlers] Failed to force resolve conflicts for ${dbKey}:`, error);
        return { error: error.message };
      }
    });

    ipcMain.handle('resolve-document-conflicts', async (event, { dbKey, docId }) => {
      try {
        return await this.databaseHandler.resolveDocumentConflicts(dbKey, docId);
      } catch (error) {
        console.error(`[IPCHandlers] Failed to resolve conflicts for ${dbKey}/${docId}:`, error);
        return { error: error.message };
      }
    });

    ipcMain.handle('get-conflict-performance-metrics', async () => {
      try {
        return await this.databaseHandler.getConflictPerformanceMetrics();
      } catch (error) {
        console.error('[IPCHandlers] Failed to get conflict performance metrics:', error);
        return { error: error.message };
      }
    });

    ipcMain.handle('predict-conflicts', async (event, { dbKey, docId, newData }) => {
      try {
        return await this.databaseHandler.predictConflicts(dbKey, docId, newData);
      } catch (error) {
        console.error(`[IPCHandlers] Failed to predict conflicts for ${dbKey}/${docId}:`, error);
        return { error: error.message };
      }
    });

    console.log('✅ Database IPC Handlers initialized successfully');
  }

  // Cleanup method
  async removeAllHandlers() {
    console.log('[IPCHandlers] Cleaning up IPC handlers...');

    // MEMORY LEAK FIX: Clean up active change listeners first
    if (this.activeChangeListeners && this.activeChangeListeners.size > 0) {
      console.log(`[IPCHandlers] Cleaning up ${this.activeChangeListeners.size} active change listeners`);

      for (const [dbKey, listenerData] of this.activeChangeListeners) {
        try {
          if (listenerData.listener) {
            // Remove specific handlers
            if (listenerData.changeHandler) {
              listenerData.listener.removeListener('change', listenerData.changeHandler);
            }
            if (listenerData.errorHandler) {
              listenerData.listener.removeListener('error', listenerData.errorHandler);
            }

            // Remove any remaining listeners
            listenerData.listener.removeAllListeners();

            // Cancel the listener
            listenerData.listener.cancel();
          }
        } catch (error) {
          console.warn(`[IPCHandlers] Error cleaning up listener for ${dbKey}:`, error);
        }
      }

      this.activeChangeListeners.clear();
    }

    const handlers = [
      'get-app-version',
      'get-system-info',
      'get-memory-usage',
      'minimize-window',
      'maximize-window',
      'close-window',
      'send-email',
      'select-file',
      'save-file',
      'get-performance-metrics',
      'open-external',
      'restart-app',
      'clear-cache',
      // Database handlers
      'db-initialize',
      'db-save',
      'db-get',
      'db-get-all',
      'db-all-docs',
      'db-delete',
      'db-bulk-save',
      'db-find',
      'db-sync-this',
      'db-get-attachment',
      'db-put-attachment',
      'db-remove-attachment',
      'db-authenticate',
      'db-dump',
      'db-create-index',
      'db-info',
      'db-compact',
      'db-sync-stats',
      'db-force-sync',
      'sync-start',
      'sync-stop',
      'sync-force-full',
      'sync-lan-specific',
      'sync-status',
      'sync-add-database',
      'sync-remove-database',
      'sync-restart-database',
      'db-add-log',
      'db-get-logs',
      'db-update-lan-details',
      'sync-trigger-instant',
      'sync-trigger',
      'sync-set-remote-url',
      'sync-test-remote-connectivity',
      'sync-get-remote-url',
      'db-close',
      'db-close-all',
      'db-get-all-databases',
      'db-add-log',
      'db-setup-change-listener',
      'db-remove-change-listener'
    ];

    handlers.forEach(handler => {
      ipcMain.removeHandler(handler);
    });

    // Cleanup database services
    if (this.syncService) {
      await this.syncService.stopSyncService();
    }
    if (this.databaseHandler) {
      await this.databaseHandler.closeAllDatabases();
    }
  }
}

module.exports = IPCHandlers;
